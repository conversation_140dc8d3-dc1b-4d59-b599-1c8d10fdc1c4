-- إعد<PERSON> قاعدة البيانات لنظام تتبع الطلبيات

-- 1. إن<PERSON>اء جدول order_tracking إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS order_tracking (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
    status TEXT,
    notes TEXT,
    received_at TIMESTAMP WITH TIME ZONE,
    processing_at TIMESTAMP WITH TIME ZONE,
    shipping_at TIMESTAMP WITH TIME ZONE,
    out_for_delivery_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(order_id)
);

-- 2. إضا<PERSON>ة الأعمدة المفقودة إذا لم تكن موجودة
ALTER TABLE order_tracking 
ADD COLUMN IF NOT EXISTS received_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS processing_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS shipping_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS out_for_delivery_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS delivered_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS cancelled_at TIMESTAMP WITH TIME ZONE;

-- 3. تحديث قيود الحالة في جدول orders
ALTER TABLE orders 
DROP CONSTRAINT IF EXISTS orders_status_check;

ALTER TABLE orders 
ADD CONSTRAINT orders_status_check 
CHECK (status IN ('pending', 'processing', 'shipping', 'out_for_delivery', 'delivered', 'cancelled'));

-- 4. إضافة المستخدم كمسؤول (استبدل البريد الإلكتروني بالبريد الصحيح)
UPDATE profiles 
SET role = 'admin' 
WHERE email = '<EMAIL>';

-- 5. إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_order_tracking_order_id ON order_tracking(order_id);
CREATE INDEX IF NOT EXISTS idx_profiles_role ON profiles(role);

-- 6. إنشاء دالة لتحديث التتبع تلقائياً
CREATE OR REPLACE FUNCTION update_order_tracking_on_status_change()
RETURNS TRIGGER AS $$
BEGIN
    -- إدراج أو تحديث سجل التتبع
    INSERT INTO order_tracking (order_id, status, updated_at)
    VALUES (NEW.id, NEW.status, NOW())
    ON CONFLICT (order_id) 
    DO UPDATE SET 
        status = NEW.status,
        updated_at = NOW();
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 7. إنشاء المشغل
DROP TRIGGER IF EXISTS trigger_update_order_tracking ON orders;
CREATE TRIGGER trigger_update_order_tracking
    AFTER INSERT OR UPDATE OF status ON orders
    FOR EACH ROW
    EXECUTE FUNCTION update_order_tracking_on_status_change();

-- 8. التحقق من الصلاحيات
SELECT email, role FROM profiles WHERE role = 'admin';

-- 9. عرض بعض الطلبيات للاختبار
SELECT id, status, created_at FROM orders ORDER BY created_at DESC LIMIT 5;

-- 10. عرض بيانات التتبع
SELECT * FROM order_tracking ORDER BY created_at DESC LIMIT 5;

-- إصل<PERSON><PERSON> مشكلة ON CONFLICT في قاعدة البيانات

-- 1. حذف الـ trigger المسبب للمشكلة
DROP TRIGGER IF EXISTS trigger_update_order_tracking ON orders;
DROP FUNCTION IF EXISTS update_order_tracking_on_status_change();

-- 2. حذف الـ trigger القديم أيضاً
DROP TRIGGER IF EXISTS create_order_tracking_on_order ON orders;
DROP FUNCTION IF EXISTS create_order_tracking();

-- 3. إنشاء دالة جديدة بسيطة لإنشاء سجل التتبع
CREATE OR REPLACE FUNCTION create_simple_order_tracking()
RETURNS TRIGGER AS $$
BEGIN
  -- إنشاء سجل تتبع بسيط بدون ON CONFLICT
  INSERT INTO order_tracking(order_id, status, created_at, updated_at)
  VALUES(NEW.id, NEW.status, NOW(), NOW());
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 4. <PERSON><PERSON><PERSON><PERSON><PERSON> trigger جديد بسيط
CREATE TRIGGER create_simple_order_tracking_trigger
AFTER INSERT ON orders
FOR EACH ROW
EXECUTE FUNCTION create_simple_order_tracking();

-- 5. التحقق من الجداول
SELECT 'orders table' as table_name, COUNT(*) as count FROM orders
UNION ALL
SELECT 'order_tracking table' as table_name, COUNT(*) as count FROM order_tracking;

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:iconsax/iconsax.dart';

class AdminDashboard extends StatelessWidget {
  const AdminDashboard({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          tr('admin_dashboard_title'),
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18.sp),
        ),
        actions: [
          IconButton(icon: const Icon(Icons.search), onPressed: () {}),
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {},
          ),
          const SizedBox(width: 8),
          CircleAvatar(
            radius: 16.r,
            backgroundColor: Theme.of(context).primaryColor.withOpacity(0.2),
            child: Icon(
              Icons.person,
              size: 20.r,
              color: Theme.of(context).primaryColor,
            ),
          ),
          const SizedBox(width: 16),
        ],
      ),
      body: Row(
        children: [
          // القائمة الجانبية
          NavigationRail(
            extended: true,
            minExtendedWidth: 220.w,
            backgroundColor: isDarkMode
                ? const Color(0xFF1E2233)
                : Colors.white,
            selectedIndex: 0,
            onDestinationSelected: (index) {},
            destinations: [
              NavigationRailDestination(
                icon: const Icon(Icons.home_outlined),
                selectedIcon: const Icon(Icons.home),
                label: Text(tr('dashboard')),
              ),
              NavigationRailDestination(
                icon: const Icon(Iconsax.car),
                selectedIcon: const Icon(Iconsax.car),
                label: Text(tr('vehicles')),
              ),
              NavigationRailDestination(
                icon: const Icon(Iconsax.profile_circle),
                selectedIcon: const Icon(Iconsax.profile_circle),
                label: Text(tr('drivers')),
              ),
              NavigationRailDestination(
                icon: const Icon(Iconsax.document),
                selectedIcon: const Icon(Iconsax.document),
                label: Text(tr('licenses')),
              ),
              NavigationRailDestination(
                icon: const Icon(Iconsax.shield),
                selectedIcon: const Icon(Iconsax.shield),
                label: Text(tr('insurance')),
              ),
              NavigationRailDestination(
                icon: const Icon(Iconsax.warning_2),
                selectedIcon: const Icon(Iconsax.warning_2),
                label: Text(tr('violations')),
              ),
              NavigationRailDestination(
                icon: const Icon(Iconsax.setting_2),
                selectedIcon: const Icon(Iconsax.setting_2),
                label: Text(tr('maintenance')),
              ),
            ],
          ),

          // المحتوى الرئيسي
          Expanded(
            child: Padding(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // عنوان الصفحة والمسار
                  Row(
                    children: [
                      Text(
                        tr('vehicle_registration_system'),
                        style: TextStyle(
                          fontSize: 20.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      Text(tr('home'), style: TextStyle(color: Colors.grey)),
                      Icon(Icons.chevron_right, size: 16.r, color: Colors.grey),
                      Text(
                        tr('dashboard'),
                        style: TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),

                  SizedBox(height: 24.h),

                  // بطاقات الإحصائيات
                  Row(
                    children: [
                      _buildStatCard(
                        context,
                        title: tr('information'),
                        count: '2',
                        icon: Icons.info_outline,
                        color: Colors.blueGrey,
                        onTap: () {},
                      ),
                      SizedBox(width: 16.w),
                      _buildStatCard(
                        context,
                        title: tr('violations'),
                        count: '2',
                        icon: Iconsax.warning_2,
                        color: Colors.teal,
                        onTap: () {},
                      ),
                      SizedBox(width: 16.w),
                      _buildStatCard(
                        context,
                        title: tr('drivers'),
                        count: '3',
                        icon: Iconsax.profile_circle,
                        color: Colors.redAccent,
                        onTap: () {},
                      ),
                      SizedBox(width: 16.w),
                      _buildStatCard(
                        context,
                        title: tr('vehicles'),
                        count: '2',
                        icon: Iconsax.car,
                        color: Colors.blue,
                        onTap: () {},
                      ),
                    ],
                  ),

                  SizedBox(height: 32.h),

                  // آخر العمليات
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // آخر المعلومات
                      Expanded(
                        child: _buildRecentActivitiesCard(
                          context,
                          title: tr('recent_information'),
                          items: [
                            RecentActivity(
                              date: '2022-02-06',
                              title: 'أم درمان - الخرطوم',
                              subtitle: '',
                            ),
                            RecentActivity(
                              date: '2022-02-03',
                              title: 'بارا',
                              subtitle: '',
                            ),
                          ],
                        ),
                      ),

                      SizedBox(width: 24.w),

                      // آخر الصيانات
                      Expanded(
                        child: _buildRecentActivitiesCard(
                          context,
                          title: tr('recent_maintenance'),
                          items: [
                            RecentActivity(
                              date: '2022-02-22',
                              title: 'الوثيقة الكبرى',
                              subtitle: '',
                            ),
                            RecentActivity(
                              date: '',
                              title: 'صيانة فرامل + مواسير الإضاءة',
                              subtitle: 'مرسيدس بنز 14 3144',
                              hasAvatar: true,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context, {
    required String title,
    required String count,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8.r),
        child: Container(
          height: 180.h,
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8.r),
          ),
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.all(8.r),
                    decoration: BoxDecoration(
                      color: color.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Icon(icon, color: color),
                  ),
                ],
              ),
              const Spacer(),
              Text(
                count,
                style: TextStyle(fontSize: 48.sp, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 16.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    tr('view_all'),
                    style: TextStyle(color: color, fontWeight: FontWeight.bold),
                  ),
                  Icon(Icons.arrow_forward, color: color, size: 16.r),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecentActivitiesCard(
    BuildContext context, {
    required String title,
    required List<RecentActivity> items,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 16.h),
          ...items.map((item) => _buildActivityItem(context, item)).toList(),
        ],
      ),
    );
  }

  Widget _buildActivityItem(BuildContext context, RecentActivity activity) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (activity.hasAvatar)
            CircleAvatar(
              radius: 20.r,
              backgroundImage: const AssetImage(
                'assets/images/avatar_placeholder.png',
              ),
            )
          else
            Container(
              width: 12.w,
              height: 12.h,
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                shape: BoxShape.circle,
              ),
            ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  activity.title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14.sp,
                  ),
                ),
                if (activity.subtitle.isNotEmpty)
                  Text(
                    activity.subtitle,
                    style: TextStyle(color: Colors.grey, fontSize: 12.sp),
                  ),
              ],
            ),
          ),
          if (activity.date.isNotEmpty)
            Text(
              activity.date,
              style: TextStyle(color: Colors.grey, fontSize: 12.sp),
            ),
        ],
      ),
    );
  }
}

class RecentActivity {
  final String date;
  final String title;
  final String subtitle;
  final bool hasAvatar;

  RecentActivity({
    required this.date,
    required this.title,
    required this.subtitle,
    this.hasAvatar = false,
  });
}

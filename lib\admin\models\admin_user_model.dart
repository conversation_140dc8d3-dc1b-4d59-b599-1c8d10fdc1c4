

/// نموذج بيانات المستخدم الإداري
class AdminUser {
  final String id;
  final String email;
  final String fullName;
  final String? phone;
  final String role; // admin, manager, operator
  final DateTime? createdAt;
  final DateTime? lastSignIn;

  AdminUser({
    required this.id,
    required this.email,
    required this.fullName,
    this.phone,
    required this.role,
    this.createdAt,
    this.lastSignIn,
  });

  factory AdminUser.fromJson(Map<String, dynamic> json) {
    return AdminUser(
      id: json['id'] ?? '',
      email: json['email'] ?? '',
      fullName: json['full_name'] ?? json['fullName'] ?? '',
      phone: json['phone'],
      role: json['role'] ?? 'user',
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at']) 
          : null,
      lastSignIn: json['last_sign_in_at'] != null 
          ? DateTime.parse(json['last_sign_in_at']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'full_name': fullName,
      'phone': phone,
      'role': role,
      'created_at': createdAt?.toIso8601String(),
      'last_sign_in_at': lastSignIn?.toIso8601String(),
    };
  }

  AdminUser copyWith({
    String? id,
    String? email,
    String? fullName,
    String? phone,
    String? role,
    DateTime? createdAt,
    DateTime? lastSignIn,
  }) {
    return AdminUser(
      id: id ?? this.id,
      email: email ?? this.email,
      fullName: fullName ?? this.fullName,
      phone: phone ?? this.phone,
      role: role ?? this.role,
      createdAt: createdAt ?? this.createdAt,
      lastSignIn: lastSignIn ?? this.lastSignIn,
    );
  }

  @override
  String toString() {
    return 'AdminUser(id: $id, email: $email, fullName: $fullName, role: $role)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
  
    return other is AdminUser &&
      other.id == id &&
      other.email == email &&
      other.fullName == fullName &&
      other.phone == phone &&
      other.role == role;
  }

  @override
  int get hashCode {
    return id.hashCode ^
      email.hashCode ^
      fullName.hashCode ^
      phone.hashCode ^
      role.hashCode;
  }
}

/// تعريف أدوار المستخدمين في النظام
class UserRoles {
  static const String admin = 'admin';       // مدير النظام (كافة الصلاحيات)
  static const String manager = 'manager';    // مدير (صلاحيات إدارية محدودة)
  static const String operator = 'operator';  // مشغل (صلاحيات تشغيلية فقط)
  static const String user = 'user';          // مستخدم عادي (بدون صلاحيات إدارية)

  /// التحقق مما إذا كان الدور المحدد يملك صلاحيات إدارية
  static bool hasAdminAccess(String role) {
    return role == admin || role == manager;
  }

  /// التحقق مما إذا كان الدور المحدد يملك صلاحيات تعديل المستخدمين
  static bool canManageUsers(String role) {
    return role == admin;
  }

  /// التحقق مما إذا كان الدور المحدد يملك صلاحيات تعديل الطلبيات
  static bool canManageOrders(String role) {
    return role == admin || role == manager || role == operator;
  }

  /// الحصول على قائمة الأدوار المتاحة للاختيار
  static List<String> getAvailableRoles(String currentUserRole) {
    if (currentUserRole == admin) {
      return [admin, manager, operator, user];
    } else if (currentUserRole == manager) {
      return [operator, user];
    } else {
      return [user];
    }
  }
}

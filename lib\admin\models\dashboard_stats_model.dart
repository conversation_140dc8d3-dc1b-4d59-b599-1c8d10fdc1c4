/// نموذج بيانات إحصائيات لوحة التحكم
class DashboardStats {
  final int totalUsers;
  final int totalOrders;
  final Map<String, int> ordersByStatus;
  final int totalQuantity;
  final double totalAmount;
  final double completedDonationsAmount;

  DashboardStats({
    required this.totalUsers,
    required this.totalOrders,
    required this.ordersByStatus,
    required this.totalQuantity,
    required this.totalAmount,
    required this.completedDonationsAmount,
  });

  factory DashboardStats.empty() {
    return DashboardStats(
      totalUsers: 0,
      totalOrders: 0,
      ordersByStatus: {
        'pending': 0,
        'processing': 0,
        'shipping': 0,
        'out_for_delivery': 0,
        'delivered': 0,
        'cancelled': 0,
      },
      totalQuantity: 0,
      totalAmount: 0.0,
      completedDonationsAmount: 0.0,
    );
  }

  factory DashboardStats.fromJson(Map<String, dynamic> json) {
    return DashboardStats(
      totalUsers: json['total_users'] ?? 0,
      totalOrders: json['total_orders'] ?? 0,
      ordersByStatus: {
        'pending': json['pending_orders'] ?? 0,
        'processing': json['processing_orders'] ?? 0,
        'shipping': json['shipping_orders'] ?? 0,
        'out_for_delivery': json['out_for_delivery_orders'] ?? 0,
        'delivered': json['delivered_orders'] ?? 0,
        'cancelled': json['cancelled_orders'] ?? 0,
      },
      totalQuantity: json['total_quantity'] ?? 0,
      totalAmount: (json['total_amount'] ?? 0).toDouble(),
      completedDonationsAmount: (json['completed_donations_amount'] ?? 0)
          .toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_users': totalUsers,
      'total_orders': totalOrders,
      'pending_orders': ordersByStatus['pending'],
      'processing_orders': ordersByStatus['processing'],
      'shipping_orders': ordersByStatus['shipping'],
      'out_for_delivery_orders': ordersByStatus['out_for_delivery'],
      'delivered_orders': ordersByStatus['delivered'],
      'cancelled_orders': ordersByStatus['cancelled'],
      'total_quantity': totalQuantity,
      'total_amount': totalAmount,
      'completed_donations_amount': completedDonationsAmount,
    };
  }

  @override
  String toString() {
    return 'DashboardStats(totalUsers: $totalUsers, totalOrders: $totalOrders, ordersByStatus: $ordersByStatus, totalQuantity: $totalQuantity, totalAmount: $totalAmount, completedDonationsAmount: $completedDonationsAmount)';
  }
}

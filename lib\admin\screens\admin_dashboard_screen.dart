import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:iconsax/iconsax.dart';
import '../../services/supabase_service.dart';
import '../models/dashboard_stats_model.dart';
import '../services/admin_service.dart';
import '../widgets/admin_sidebar.dart';
import 'admin_login_screen.dart';
import 'admin_users_screen.dart';
import 'admin_orders_screen.dart';
import 'admin_settings_screen.dart';

class AdminDashboardScreen extends StatefulWidget {
  const AdminDashboardScreen({Key? key}) : super(key: key);

  @override
  State<AdminDashboardScreen> createState() => _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends State<AdminDashboardScreen> {
  bool _isLoading = true;
  DashboardStats _stats = DashboardStats.empty();
  int _selectedIndex = 0;

  late final List<Widget> _screens;

  // بيانات الطلبات الحديثة
  List<Map<String, dynamic>> _recentOrders = [];

  @override
  void initState() {
    super.initState();
    _loadData();

    _screens = [
      _buildDashboardContent(),
      const AdminUsersScreen(),
      const AdminOrdersScreen(),
      const AdminSettingsScreen(),
    ];
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // جلب الإحصائيات
      final stats = await AdminService.getDashboardStats();
      
      // جلب آخر 5 طلبيات
      final orders = await AdminService.getAllOrders();
      final recentOrders = orders.take(5).toList();

      if (mounted) {
        setState(() {
          _stats = stats;
          _recentOrders = recentOrders;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل البيانات: ${e.toString()}')),
        );
      }
    }
  }

  Future<void> _signOut() async {
    try {
      await SupabaseService.client.auth.signOut();
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const AdminLoginScreen()),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تسجيل الخروج: ${e.toString()}')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    _screens[0] = _buildDashboardContent();

    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Text(tr('admin_panel')),
            const Spacer(),
            IconButton(
              icon: const Icon(Iconsax.refresh),
              onPressed: _loadData,
              tooltip: tr('refresh'),
            ),
          ],
        ),
        automaticallyImplyLeading: false,
      ),
      body: Row(
        children: [
          AdminSidebar(
            selectedIndex: _selectedIndex,
            onItemSelected: (index) {
              if (index == -1) {
                _signOut();
              } else {
                setState(() {
                  _selectedIndex = index;
                });
              }
            },
          ),
          Expanded(
            child: Container(
              color: const Color(0xFFF5F5F5),
              child: _screens[_selectedIndex],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDashboardContent() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              tr('dashboard'),
              style: TextStyle(
                fontSize: 24.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            // صف البطاقات الإحصائية
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    title: tr('total_users'),
                    value: _stats.totalUsers.toString(),
                    subtitle: tr('present_user'),
                    percentage: "60%",
                    color: const Color(0xFF3F51B5),
                    icon: Iconsax.user,
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: _buildStatCard(
                    title: tr('total_orders'),
                    value: _stats.totalOrders.toString(),
                    subtitle: tr('submitted_tickets'),
                    percentage: "60%",
                    color: const Color(0xFFFF9800),
                    icon: Iconsax.shopping_cart,
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: _buildStatCard(
                    title: tr('pending_orders'),
                    value: _stats.ordersByStatus['pending']?.toString() ?? '0',
                    subtitle: tr('pending_tickets'),
                    percentage: "60%",
                    color: const Color(0xFF4CAF50),
                    icon: Iconsax.timer,
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: _buildStatCard(
                    title: tr('delivered_orders'),
                    value: _stats.ordersByStatus['delivered']?.toString() ?? '0',
                    subtitle: tr('data_usage'),
                    percentage: "100 GB",
                    color: const Color(0xFFF44336),
                    icon: Iconsax.driver,
                  ),
                ),
              ],
            ),
            SizedBox(height: 24.h),
            // الجدول
            _buildRecentRecordsTable(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required String subtitle,
    required String percentage,
    required Color color,
    required IconData icon,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.grey[700],
                  ),
                ),
                Container(
                  padding: EdgeInsets.all(8.w),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 20.r,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8.h),
            Text(
              value,
              style: TextStyle(
                fontSize: 24.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 4.h),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 12.h),
            Row(
              children: [
                Expanded(
                  flex: 6,
                  child: LinearProgressIndicator(
                    value: 0.6,
                    backgroundColor: color.withOpacity(0.2),
                    color: color,
                    minHeight: 6.h,
                    borderRadius: BorderRadius.circular(3.r),
                  ),
                ),
                SizedBox(width: 8.w),
                Expanded(
                  flex: 4,
                  child: Text(
                    percentage,
                    style: TextStyle(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentRecordsTable() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          tr('recent_records'),
          style: TextStyle(
            fontSize: 20.sp,
            fontWeight: FontWeight.bold,
            color: const Color(0xFF2D5F7B),
          ),
        ),
        SizedBox(height: 16.h),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: DataTable(
            columnSpacing: 20.w,
            columns: [
              DataColumn(
                label: Text(
                  tr('title'),
                  style: const TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
                ),
              ),
              DataColumn(
                label: Text(
                  tr('order_date'),
                  style: const TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
                ),
              ),
              DataColumn(
                label: Text(
                  tr('order_notes'),
                  style: const TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
                ),
              ),
            ],
            rows: _recentOrders.map((order) {
              return DataRow(
                cells: [
                  DataCell(Text('#${order['id']}')),
                  DataCell(Text(
                    DateTime.parse(order['created_at'])
                        .toLocal()
                        .toString()
                        .split(' ')[0],
                  )),
                  DataCell(Text(order['status'].toString().tr())),
                ],
              );
            }).toList(),
            headingRowColor: MaterialStateProperty.all(const Color(0xFF2D5F7B)),
            headingTextStyle: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.white,
              fontSize: 14.sp,
            ),
            dataRowMinHeight: 60.h,
          ),
        ),
      ],
    );
  }
}

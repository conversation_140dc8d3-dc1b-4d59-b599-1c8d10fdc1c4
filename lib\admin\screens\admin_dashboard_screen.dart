import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:iconsax/iconsax.dart';
import '../../services/supabase_service.dart';
import '../models/dashboard_stats_model.dart';
import '../services/admin_service.dart';
import '../widgets/admin_sidebar.dart';
import 'admin_login_screen.dart';
import 'admin_users_screen.dart';
import 'admin_orders_screen.dart';
import 'admin_settings_screen.dart';

class AdminDashboardScreen extends StatefulWidget {
  const AdminDashboardScreen({Key? key}) : super(key: key);

  @override
  State<AdminDashboardScreen> createState() => _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends State<AdminDashboardScreen> {
  bool _isLoading = true;
  DashboardStats _stats = DashboardStats.empty();
  int _selectedIndex = 0;

  late final List<Widget> _screens;

  // بيانات الطلبات الحديثة
  List<Map<String, dynamic>> _recentOrders = [];

  @override
  void initState() {
    super.initState();
    _loadData();

    _screens = [
      _buildDashboardContent(),
      const AdminUsersScreen(),
      const AdminOrdersScreen(),
      const AdminSettingsScreen(),
    ];
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // جلب الإحصائيات
      final stats = await AdminService.getDashboardStats();

      // جلب آخر 5 طلبيات
      final orders = await AdminService.getAllOrders();
      final recentOrders = orders.take(5).toList();

      if (mounted) {
        setState(() {
          _stats = stats;
          _recentOrders = recentOrders;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل البيانات: ${e.toString()}')),
        );
      }
    }
  }

  Future<void> _signOut() async {
    try {
      await SupabaseService.client.auth.signOut();
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const AdminLoginScreen()),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تسجيل الخروج: ${e.toString()}')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    _screens[0] = _buildDashboardContent();

    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Text(tr('admin_panel')),
            const Spacer(),
            IconButton(
              icon: const Icon(Iconsax.refresh),
              onPressed: _loadData,
              tooltip: tr('refresh'),
            ),
          ],
        ),
        automaticallyImplyLeading: true, // السماح بإظهار زر القائمة
      ),
      body: LayoutBuilder(
        builder: (context, constraints) {
          // تحديد ما إذا كانت الشاشة صغيرة
          bool isSmallScreen = constraints.maxWidth < 768;

          if (isSmallScreen) {
            // للشاشات الصغيرة: عرض المحتوى فقط مع drawer
            return Container(
              color: const Color(0xFFF5F5F5),
              child: _screens[_selectedIndex],
            );
          } else {
            // للشاشات الكبيرة: عرض الشريط الجانبي والمحتوى
            return Row(
              children: [
                AdminSidebar(
                  selectedIndex: _selectedIndex,
                  onItemSelected: (index) {
                    if (index == -1) {
                      _signOut();
                    } else {
                      setState(() {
                        _selectedIndex = index;
                      });
                    }
                  },
                ),
                Expanded(
                  child: Container(
                    color: const Color(0xFFF5F5F5),
                    child: _screens[_selectedIndex],
                  ),
                ),
              ],
            );
          }
        },
      ),
      drawer: MediaQuery.of(context).size.width < 768
          ? Drawer(
              child: AdminSidebar(
                selectedIndex: _selectedIndex,
                onItemSelected: (index) {
                  Navigator.of(context).pop(); // إغلاق الـ drawer
                  if (index == -1) {
                    _signOut();
                  } else {
                    setState(() {
                      _selectedIndex = index;
                    });
                  }
                },
              ),
            )
          : null,
    );
  }

  Widget _buildDashboardContent() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: EdgeInsets.symmetric(vertical: 16.h),
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                decoration: BoxDecoration(
                  color: const Color(0xFF38B2AC),
                  borderRadius: BorderRadius.circular(20.r),
                ),
                child: Text(
                  'الرئيسية',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
            SizedBox(height: 16.h),
            // صف البطاقات الإحصائية
            LayoutBuilder(
              builder: (context, constraints) {
                // تحديد عدد الأعمدة حسب عرض الشاشة
                int crossAxisCount = 3;
                if (constraints.maxWidth < 1400) crossAxisCount = 2;
                if (constraints.maxWidth < 800) crossAxisCount = 1;

                return GridView.count(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  crossAxisCount: crossAxisCount,
                  crossAxisSpacing: 16.w,
                  mainAxisSpacing: 16.h,
                  childAspectRatio: crossAxisCount == 1
                      ? 4.0
                      : (crossAxisCount == 2 ? 2.5 : 2.0),
                  children: [
                    _buildStatCard(
                      title: tr('total_users'),
                      value: _stats.totalUsers.toString(),
                      subtitle: tr('present_user'),
                      percentage: "اكثر المستخدمين",
                      color: const Color(0xFF4A5568), // رمادي داكن
                      icon: Iconsax.user,
                    ),
                    _buildStatCard(
                      title: tr('total_orders'),
                      value: _stats.totalOrders.toString(),
                      subtitle: tr('submitted_tickets'),
                      percentage: "اكثر المداخلات",
                      color: const Color(0xFF38B2AC), // أخضر فيروزي
                      icon: Iconsax.shopping_cart,
                    ),
                    _buildStatCard(
                      title: tr('pending_orders'),
                      value:
                          _stats.ordersByStatus['pending']?.toString() ?? '0',
                      subtitle: tr('pending_tickets'),
                      percentage: "اكثر السائقين",
                      color: const Color(0xFFE53E3E), // أحمر
                      icon: Iconsax.timer,
                    ),
                    _buildStatCard(
                      title: tr('delivered_orders'),
                      value:
                          _stats.ordersByStatus['delivered']?.toString() ?? '0',
                      subtitle: tr('data_usage'),
                      percentage: "اكثر الشركات",
                      color: const Color(0xFF805AD5), // بنفسجي
                      icon: Iconsax.driver,
                    ),
                    _buildStatCard(
                      title: 'إجمالي الكميات',
                      value: _stats.totalQuantity.toString(),
                      subtitle: 'إجمالي كميات الطلبات',
                      percentage: "جميع الكميات",
                      color: const Color(0xFF38A169), // أخضر
                      icon: Iconsax.box,
                    ),
                    _buildStatCard(
                      title: 'إجمالي التبرعات',
                      value: '${_stats.totalAmount.toStringAsFixed(2)} درهم',
                      subtitle: 'مبلغ التبرعات المكتملة',
                      percentage: "التبرعات المدفوعة",
                      color: const Color(0xFFD69E2E), // ذهبي
                      icon: Iconsax.money_send,
                    ),
                  ],
                );
              },
            ),
            SizedBox(height: 24.h),

            // سطر إجمالي التبرعات المكتملة
            Container(
              padding: EdgeInsets.all(20.w),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    const Color(0xFF38B2AC),
                    const Color(0xFF38B2AC).withValues(alpha: 0.8),
                  ],
                ),
                borderRadius: BorderRadius.circular(16.r),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF38B2AC).withValues(alpha: 0.3),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(12.w),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Icon(
                      Iconsax.money_recive,
                      color: Colors.white,
                      size: 24.r,
                    ),
                  ),
                  SizedBox(width: 16.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'إجمالي التبرعات المكتملة',
                          style: TextStyle(
                            fontSize: 18.sp,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          'المبلغ الإجمالي للتبرعات التي تم دفعها بنجاح',
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: Colors.white.withValues(alpha: 0.9),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 16.w,
                      vertical: 8.h,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(20.r),
                    ),
                    child: Text(
                      '${_stats.completedDonationsAmount.toStringAsFixed(2)} درهم إماراتي',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF38B2AC),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: 24.h),
            // الجدول
            _buildRecentRecordsTable(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required String subtitle,
    required String percentage,
    required Color color,
    required IconData icon,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // تحديد أحجام النصوص حسب عرض البطاقة
        double iconSize = constraints.maxWidth < 150
            ? 18.r
            : (constraints.maxWidth < 200 ? 20.r : 24.r);
        double valueSize = constraints.maxWidth < 150
            ? 20.sp
            : (constraints.maxWidth < 200 ? 24.sp : 32.sp);
        double titleSize = constraints.maxWidth < 150
            ? 10.sp
            : (constraints.maxWidth < 200 ? 12.sp : 14.sp);
        double subtitleSize = constraints.maxWidth < 150
            ? 8.sp
            : (constraints.maxWidth < 200 ? 10.sp : 12.sp);
        double percentageSize = constraints.maxWidth < 150
            ? 10.sp
            : (constraints.maxWidth < 200 ? 12.sp : 14.sp);
        double padding = constraints.maxWidth < 150
            ? 12.w
            : (constraints.maxWidth < 200 ? 16.w : 20.w);

        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [color, color.withValues(alpha: 0.8)],
            ),
            borderRadius: BorderRadius.circular(16.r),
            boxShadow: [
              BoxShadow(
                color: color.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Padding(
            padding: EdgeInsets.all(padding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        title,
                        style: TextStyle(
                          fontSize: titleSize,
                          color: Colors.white.withValues(alpha: 0.9),
                          fontWeight: FontWeight.w500,
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.all(
                        constraints.maxWidth < 200 ? 8.w : 10.w,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      child: Icon(icon, color: Colors.white, size: iconSize),
                    ),
                  ],
                ),
                SizedBox(height: 16.h),
                FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(
                    value,
                    style: TextStyle(
                      fontSize: valueSize,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: subtitleSize,
                    color: Colors.white.withValues(alpha: 0.8),
                    fontWeight: FontWeight.w400,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
                SizedBox(height: 12.h),
                Row(
                  children: [
                    Expanded(
                      flex: 3,
                      child: Text(
                        percentage,
                        style: TextStyle(
                          fontSize: percentageSize,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                    SizedBox(width: 4.w),
                    Icon(Iconsax.arrow_up_3, color: Colors.white, size: 14.r),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildRecentRecordsTable() {
    return LayoutBuilder(
      builder: (context, constraints) {
        bool isSmallScreen = constraints.maxWidth < 800;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    'اخر العمليات',
                    style: TextStyle(
                      fontSize: isSmallScreen ? 20.sp : 24.sp,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF2C3E50),
                    ),
                  ),
                ),
                if (!isSmallScreen)
                  Text(
                    'المعلومات',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF2C3E50),
                    ),
                  ),
              ],
            ),
            SizedBox(height: 20.h),
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(16.r),
                child: isSmallScreen
                    ? _buildMobileTable()
                    : _buildDesktopTable(),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildDesktopTable() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        columnSpacing: 30.w,
        headingRowHeight: 60.h,
        dataRowMinHeight: 50.h,
        dataRowMaxHeight: 60.h,
        columns: [
          DataColumn(
            label: Text(
              'التاريخ الكبري',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: 16.sp,
              ),
            ),
          ),
          DataColumn(
            label: Text(
              'التاريخ',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: 16.sp,
              ),
            ),
          ),
          DataColumn(
            label: Text(
              'ملاحظات',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: 16.sp,
              ),
            ),
          ),
        ],
        rows: _recentOrders.take(5).map((order) {
          return DataRow(
            cells: [
              DataCell(
                Row(
                  children: [
                    CircleAvatar(
                      radius: 20.r,
                      backgroundColor: const Color(0xFF38B2AC),
                      child: Icon(
                        Iconsax.user,
                        color: Colors.white,
                        size: 20.r,
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: Text(
                        '${DateTime.parse(order['created_at']).day}-${DateTime.parse(order['created_at']).month}-${DateTime.parse(order['created_at']).year}',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 14.sp,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
              DataCell(
                Text(
                  DateTime.parse(
                    order['created_at'],
                  ).toLocal().toString().split(' ')[0],
                  style: TextStyle(fontSize: 14.sp, color: Colors.grey[600]),
                ),
              ),
              DataCell(
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 12.w,
                    vertical: 6.h,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusColor(
                      order['status'],
                    ).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: Text(
                    order['status'].toString().tr(),
                    style: TextStyle(
                      color: _getStatusColor(order['status']),
                      fontWeight: FontWeight.w600,
                      fontSize: 12.sp,
                    ),
                  ),
                ),
              ),
            ],
          );
        }).toList(),
        headingRowColor: WidgetStateProperty.all(const Color(0xFF2C3E50)),
      ),
    );
  }

  Widget _buildMobileTable() {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: _recentOrders.take(5).length,
      itemBuilder: (context, index) {
        final order = _recentOrders[index];
        return Container(
          margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(color: Colors.grey[200]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    radius: 16.r,
                    backgroundColor: const Color(0xFF38B2AC),
                    child: Icon(Iconsax.user, color: Colors.white, size: 16.r),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Text(
                      '${DateTime.parse(order['created_at']).day}-${DateTime.parse(order['created_at']).month}-${DateTime.parse(order['created_at']).year}',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 14.sp,
                      ),
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.w,
                      vertical: 4.h,
                    ),
                    decoration: BoxDecoration(
                      color: _getStatusColor(
                        order['status'],
                      ).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(16.r),
                    ),
                    child: Text(
                      order['status'].toString().tr(),
                      style: TextStyle(
                        color: _getStatusColor(order['status']),
                        fontWeight: FontWeight.w600,
                        fontSize: 10.sp,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 8.h),
              Text(
                DateTime.parse(
                  order['created_at'],
                ).toLocal().toString().split(' ')[0],
                style: TextStyle(fontSize: 12.sp, color: Colors.grey[600]),
              ),
            ],
          ),
        );
      },
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'pending':
        return const Color(0xFFFF9800);
      case 'processing':
        return const Color(0xFF2196F3);
      case 'delivering':
        return const Color(0xFF9C27B0);
      case 'delivered':
        return const Color(0xFF4CAF50);
      case 'cancelled':
        return const Color(0xFFF44336);
      default:
        return Colors.grey;
    }
  }
}

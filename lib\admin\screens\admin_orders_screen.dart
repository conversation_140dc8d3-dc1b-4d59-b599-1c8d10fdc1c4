import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:iconsax/iconsax.dart';
import '../models/admin_user_model.dart';
import '../services/admin_service.dart';
import 'order_details_screen.dart';

class AdminOrdersScreen extends StatefulWidget {
  const AdminOrdersScreen({Key? key}) : super(key: key);

  @override
  State<AdminOrdersScreen> createState() => _AdminOrdersScreenState();
}

class _AdminOrdersScreenState extends State<AdminOrdersScreen> {
  bool _isLoading = true;
  List<Map<String, dynamic>> _orders = [];
  List<Map<String, dynamic>> _filteredOrders = [];
  String _currentUserRole = '';
  String _searchQuery = '';
  String _statusFilter = 'all';

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل دور المستخدم الحالي
      final role = await AdminService.getCurrentUserRole();
      
      // تحميل قائمة الطلبيات
      final orders = await AdminService.getAllOrders();
      
      if (mounted) {
        setState(() {
          _currentUserRole = role;
          _orders = orders;
          _applyFilters();
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل بيانات الطلبيات: ${e.toString()}')),
        );
      }
    }
  }

  void _applyFilters() {
    setState(() {
      _filteredOrders = _orders.where((order) {
        // تطبيق فلتر البحث
        final searchMatch = _searchQuery.isEmpty ||
            order['id'].toString().contains(_searchQuery) ||
            (order['profiles'] != null &&
                order['profiles']['full_name']
                    .toString()
                    .toLowerCase()
                    .contains(_searchQuery.toLowerCase()));

        // تطبيق فلتر الحالة
        final statusMatch = _statusFilter == 'all' ||
            order['status'].toString() == _statusFilter;

        return searchMatch && statusMatch;
      }).toList();
    });
  }

  void _filterOrders(String query) {
    setState(() {
      _searchQuery = query;
      _applyFilters();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // شريط البحث والفلترة
          Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              children: [
                // حقل البحث
                TextField(
                  decoration: InputDecoration(
                    hintText: tr('search_orders'),
                    prefixIcon: const Icon(Iconsax.search_normal),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    contentPadding: EdgeInsets.symmetric(vertical: 12.h),
                  ),
                  onChanged: _filterOrders,
                ),
                SizedBox(height: 12.h),
                
                // فلتر الحالة
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      _buildStatusFilterChip('all', tr('all')),
                      SizedBox(width: 8.w),
                      _buildStatusFilterChip('pending', tr('pending')),
                      SizedBox(width: 8.w),
                      _buildStatusFilterChip('processing', tr('processing')),
                      SizedBox(width: 8.w),
                      _buildStatusFilterChip('delivering', tr('delivering')),
                      SizedBox(width: 8.w),
                      _buildStatusFilterChip('delivered', tr('delivered')),
                      SizedBox(width: 8.w),
                      _buildStatusFilterChip('cancelled', tr('cancelled')),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          // قائمة الطلبيات
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredOrders.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Iconsax.shopping_cart,
                              size: 64.r,
                              color: Colors.grey,
                            ),
                            SizedBox(height: 16.h),
                            Text(
                              _searchQuery.isEmpty && _statusFilter == 'all'
                                  ? tr('no_orders_found')
                                  : tr('no_search_results'),
                              style: TextStyle(
                                fontSize: 16.sp,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      )
                    : RefreshIndicator(
                        onRefresh: _loadData,
                        child: ListView.builder(
                          padding: EdgeInsets.all(16.w),
                          itemCount: _filteredOrders.length,
                          itemBuilder: (context, index) {
                            final order = _filteredOrders[index];
                            return _buildOrderCard(order);
                          },
                        ),
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusFilterChip(String status, String label) {
    final isSelected = _statusFilter == status;
    
    return FilterChip(
      selected: isSelected,
      label: Text(label),
      labelStyle: TextStyle(
        color: isSelected ? Colors.white : Colors.black,
        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
      ),
      backgroundColor: Colors.grey[200],
      selectedColor: Theme.of(context).primaryColor,
      onSelected: (selected) {
        setState(() {
          _statusFilter = status;
          _applyFilters();
        });
      },
    );
  }

  Widget _buildOrderCard(Map<String, dynamic> order) {
    final orderId = order['id'] ?? '';
    final orderStatus = order['status'] ?? 'pending';
    final orderDate = order['created_at'] != null
        ? DateFormat('yyyy-MM-dd HH:mm').format(DateTime.parse(order['created_at']))
        : tr('unknown');
    final customerName = order['profiles'] != null
        ? order['profiles']['full_name'] ?? tr('unknown')
        : tr('unknown');
    final quantity = order['quantity'] ?? 0;
    final totalAmount = order['total_amount'] ?? order['total_price'] ?? 0.0;
    
    return Card(
      margin: EdgeInsets.only(bottom: 16.h),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: InkWell(
        onTap: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => OrderDetailsScreen(
                orderId: orderId,
                orderData: order,
              ),
            ),
          ).then((_) => _loadData()); // إعادة تحميل البيانات عند العودة
        },
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات الطلبية الأساسية
              Row(
                children: [
                  // رمز حالة الطلبية
                  Container(
                    padding: EdgeInsets.all(12.w),
                    decoration: BoxDecoration(
                      color: _getStatusColor(orderStatus).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Icon(
                      _getStatusIcon(orderStatus),
                      color: _getStatusColor(orderStatus),
                      size: 24.r,
                    ),
                  ),
                  SizedBox(width: 16.w),
                  
                  // معلومات الطلبية
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // رقم الطلبية
                        Text(
                          '${tr('order_id')}: $orderId',
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 4.h),
                        
                        // اسم العميل
                        Text(
                          customerName,
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // زر تعديل الطلبية
                  if (UserRoles.canManageOrders(_currentUserRole))
                    IconButton(
                      icon: const Icon(Iconsax.edit),
                      onPressed: () {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => OrderDetailsScreen(
                              orderId: orderId,
                              orderData: order,
                              isEditing: true,
                            ),
                          ),
                        ).then((_) => _loadData());
                      },
                      tooltip: tr('edit_order'),
                    ),
                ],
              ),
              SizedBox(height: 16.h),
              
              // معلومات إضافية
              Wrap(
                spacing: 16.w,
                runSpacing: 8.h,
                children: [
                  _buildInfoChip(
                    label: tr('status'),
                    value: _getStatusTranslation(orderStatus),
                    icon: _getStatusIcon(orderStatus),
                    color: _getStatusColor(orderStatus),
                  ),
                  _buildInfoChip(
                    label: tr('date'),
                    value: orderDate,
                    icon: Iconsax.calendar,
                    color: Colors.blue,
                  ),
                  _buildInfoChip(
                    label: tr('quantity'),
                    value: quantity.toString(),
                    icon: Iconsax.box,
                    color: Colors.orange,
                  ),
                  _buildInfoChip(
                    label: tr('amount'),
                    value: '$totalAmount ${tr('currency')}',
                    icon: Iconsax.money,
                    color: Colors.green,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoChip({
    required String label,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16.r,
            color: color,
          ),
          SizedBox(width: 8.w),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Colors.grey[600],
                ),
              ),
              Text(
                value,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'pending':
        return Iconsax.timer;
      case 'processing':
        return Iconsax.setting;
      case 'delivering':
        return Iconsax.truck;
      case 'delivered':
        return Iconsax.tick_circle;
      case 'cancelled':
        return Iconsax.close_circle;
      default:
        return Iconsax.timer;
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'pending':
        return Colors.amber;
      case 'processing':
        return Colors.blue;
      case 'delivering':
        return Colors.orange;
      case 'delivered':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getStatusTranslation(String status) {
    switch (status) {
      case 'pending':
        return tr('pending');
      case 'processing':
        return tr('processing');
      case 'delivering':
        return tr('delivering');
      case 'delivered':
        return tr('delivered');
      case 'cancelled':
        return tr('cancelled');
      default:
        return tr('unknown');
    }
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:iconsax/iconsax.dart';

class AdminSettingsScreen extends StatefulWidget {
  const AdminSettingsScreen({Key? key}) : super(key: key);

  @override
  State<AdminSettingsScreen> createState() => _AdminSettingsScreenState();
}

class _AdminSettingsScreenState extends State<AdminSettingsScreen> {
  bool _isDarkMode = false;
  String _selectedLanguage = 'ar';

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  void _loadSettings() {
    setState(() {
      _isDarkMode = Theme.of(context).brightness == Brightness.dark;
      _selectedLanguage = context.locale.languageCode;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان الصفحة
          Text(
            tr('admin_settings'),
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 24.h),
          
          // إعدادات المظهر
          _buildSettingsCard(
            title: tr('appearance_settings'),
            children: [
              // وضع الظلام
              _buildSwitchTile(
                title: tr('dark_mode'),
                subtitle: tr('enable_dark_mode'),
                value: _isDarkMode,
                onChanged: (value) {
                  setState(() {
                    _isDarkMode = value;
                  });
                  // هنا يمكن إضافة منطق لتغيير السمة
                },
                icon: Iconsax.moon,
              ),
              
              const Divider(),
              
              // اللغة
              _buildLanguageSelector(),
            ],
          ),
          
          SizedBox(height: 16.h),
          
          // إعدادات النظام
          _buildSettingsCard(
            title: tr('system_settings'),
            children: [
              // إعدادات الإشعارات
              _buildSwitchTile(
                title: tr('notifications'),
                subtitle: tr('enable_notifications'),
                value: true,
                onChanged: (value) {
                  // هنا يمكن إضافة منطق لتغيير إعدادات الإشعارات
                },
                icon: Iconsax.notification,
              ),
              
              const Divider(),
              
              // النسخ الاحتياطي
              ListTile(
                leading: Container(
                  padding: EdgeInsets.all(8.w),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Icon(
                    Iconsax.cloud,
                    color: Colors.blue,
                    size: 24.r,
                  ),
                ),
                title: Text(tr('backup_data')),
                subtitle: Text(tr('create_backup')),
                trailing: Icon(
                  Icons.arrow_forward_ios,
                  size: 16.r,
                  color: Colors.grey,
                ),
                onTap: () {
                  // هنا يمكن إضافة منطق للنسخ الاحتياطي
                },
              ),
            ],
          ),
          
          SizedBox(height: 16.h),
          
          // معلومات التطبيق
          _buildSettingsCard(
            title: tr('app_info'),
            children: [
              ListTile(
                title: Text(tr('app_version')),
                trailing: const Text('1.0.0'),
              ),
              const Divider(),
              ListTile(
                title: Text(tr('about_app')),
                trailing: Icon(
                  Icons.arrow_forward_ios,
                  size: 16.r,
                  color: Colors.grey,
                ),
                onTap: () {
                  // هنا يمكن إضافة منطق لعرض معلومات عن التطبيق
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsCard({required String title, required List<Widget> children}) {
    return Card(
      margin: EdgeInsets.only(bottom: 16.h),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.all(16.w),
            child: Text(
              title,
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const Divider(height: 1),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
    required IconData icon,
  }) {
    return ListTile(
      leading: Container(
        padding: EdgeInsets.all(8.w),
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Icon(
          icon,
          color: Theme.of(context).primaryColor,
          size: 24.r,
        ),
      ),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: Theme.of(context).primaryColor,
      ),
    );
  }

  Widget _buildLanguageSelector() {
    return ListTile(
      leading: Container(
        padding: EdgeInsets.all(8.w),
        decoration: BoxDecoration(
          color: Colors.orange.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Icon(
          Iconsax.language_square,
          color: Colors.orange,
          size: 24.r,
        ),
      ),
      title: Text(tr('language')),
      subtitle: Text(_selectedLanguage == 'ar' ? 'العربية' : 'English'),
      trailing: DropdownButton<String>(
        value: _selectedLanguage,
        underline: const SizedBox(),
        icon: const Icon(Icons.arrow_drop_down),
        onChanged: (String? newValue) {
          if (newValue != null) {
            setState(() {
              _selectedLanguage = newValue;
            });
            context.setLocale(Locale(newValue));
          }
        },
        items: <String>['ar', 'en'].map<DropdownMenuItem<String>>((String value) {
          return DropdownMenuItem<String>(
            value: value,
            child: Text(value == 'ar' ? 'العربية' : 'English'),
          );
        }).toList(),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:iconsax/iconsax.dart';
import '../models/admin_user_model.dart';
import '../services/admin_service.dart';

class AdminUsersScreen extends StatefulWidget {
  const AdminUsersScreen({Key? key}) : super(key: key);

  @override
  State<AdminUsersScreen> createState() => _AdminUsersScreenState();
}

class _AdminUsersScreenState extends State<AdminUsersScreen> {
  bool _isLoading = true;
  List<AdminUser> _users = [];
  String _currentUserRole = '';
  String _searchQuery = '';
  List<AdminUser> _filteredUsers = [];

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل دور المستخدم الحالي
      final role = await AdminService.getCurrentUserRole();
      
      // تحميل قائمة المستخدمين
      final users = await AdminService.getAllUsers();
      
      if (mounted) {
        setState(() {
          _currentUserRole = role;
          _users = users;
          _filteredUsers = users;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل بيانات المستخدمين: ${e.toString()}')),
        );
      }
    }
  }

  void _filterUsers(String query) {
    setState(() {
      _searchQuery = query;
      if (query.isEmpty) {
        _filteredUsers = _users;
      } else {
        _filteredUsers = _users.where((user) {
          return user.fullName.toLowerCase().contains(query.toLowerCase()) ||
              user.email.toLowerCase().contains(query.toLowerCase()) ||
              (user.phone != null && user.phone!.contains(query));
        }).toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // شريط البحث
          Padding(
            padding: EdgeInsets.all(16.w),
            child: TextField(
              decoration: InputDecoration(
                hintText: tr('search_users'),
                prefixIcon: const Icon(Iconsax.search_normal),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
                contentPadding: EdgeInsets.symmetric(vertical: 12.h),
              ),
              onChanged: _filterUsers,
            ),
          ),
          
          // قائمة المستخدمين
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredUsers.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Iconsax.user_minus,
                              size: 64.r,
                              color: Colors.grey,
                            ),
                            SizedBox(height: 16.h),
                            Text(
                              _searchQuery.isEmpty
                                  ? tr('no_users_found')
                                  : tr('no_search_results'),
                              style: TextStyle(
                                fontSize: 16.sp,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      )
                    : RefreshIndicator(
                        onRefresh: _loadData,
                        child: ListView.builder(
                          padding: EdgeInsets.all(16.w),
                          itemCount: _filteredUsers.length,
                          itemBuilder: (context, index) {
                            final user = _filteredUsers[index];
                            return _buildUserCard(user);
                          },
                        ),
                      ),
          ),
        ],
      ),
      floatingActionButton: UserRoles.canManageUsers(_currentUserRole)
          ? FloatingActionButton(
              onPressed: () {
                // إضافة مستخدم جديد (يمكن تنفيذها لاحقاً)
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text(tr('feature_coming_soon'))),
                );
              },
              child: const Icon(Iconsax.add),
            )
          : null,
    );
  }

  Widget _buildUserCard(AdminUser user) {
    final dateFormat = DateFormat('yyyy-MM-dd HH:mm');
    final createdAt = user.createdAt != null
        ? dateFormat.format(user.createdAt!)
        : tr('unknown');
    final lastSignIn = user.lastSignIn != null
        ? dateFormat.format(user.lastSignIn!)
        : tr('never');

    return Card(
      margin: EdgeInsets.only(bottom: 16.h),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات المستخدم الأساسية
            Row(
              children: [
                CircleAvatar(
                  radius: 24.r,
                  backgroundColor: _getRoleColor(user.role).withOpacity(0.1),
                  child: Text(
                    user.fullName.isNotEmpty ? user.fullName[0].toUpperCase() : '?',
                    style: TextStyle(
                      color: _getRoleColor(user.role),
                      fontWeight: FontWeight.bold,
                      fontSize: 18.sp,
                    ),
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        user.fullName,
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        user.email,
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                // زر تعديل المستخدم
                if (UserRoles.canManageUsers(_currentUserRole))
                  IconButton(
                    icon: const Icon(Iconsax.edit),
                    onPressed: () => _showEditUserDialog(user),
                    tooltip: tr('edit_user'),
                  ),
              ],
            ),
            SizedBox(height: 16.h),
            
            // معلومات إضافية
            Wrap(
              spacing: 16.w,
              runSpacing: 8.h,
              children: [
                _buildInfoChip(
                  label: tr('role'),
                  value: _getRoleTranslation(user.role),
                  icon: Iconsax.user_tag,
                  color: _getRoleColor(user.role),
                ),
                if (user.phone != null && user.phone!.isNotEmpty)
                  _buildInfoChip(
                    label: tr('phone'),
                    value: user.phone!,
                    icon: Iconsax.call,
                    color: Colors.blue,
                  ),
                _buildInfoChip(
                  label: tr('created_at'),
                  value: createdAt,
                  icon: Iconsax.calendar,
                  color: Colors.green,
                ),
                _buildInfoChip(
                  label: tr('last_login'),
                  value: lastSignIn,
                  icon: Iconsax.login,
                  color: Colors.purple,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoChip({
    required String label,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16.r,
            color: color,
          ),
          SizedBox(width: 8.w),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Colors.grey[600],
                ),
              ),
              Text(
                value,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _showEditUserDialog(AdminUser user) async {
    final formKey = GlobalKey<FormState>();
    final nameController = TextEditingController(text: user.fullName);
    final phoneController = TextEditingController(text: user.phone ?? '');
    String selectedRole = user.role;

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(tr('edit_user')),
        content: Form(
          key: formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // حقل الاسم الكامل
                TextFormField(
                  controller: nameController,
                  decoration: InputDecoration(
                    labelText: tr('full_name'),
                    prefixIcon: const Icon(Iconsax.user),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return tr('name_required');
                    }
                    return null;
                  },
                ),
                SizedBox(height: 16.h),
                
                // حقل رقم الهاتف
                TextFormField(
                  controller: phoneController,
                  decoration: InputDecoration(
                    labelText: tr('phone'),
                    prefixIcon: const Icon(Iconsax.call),
                  ),
                ),
                SizedBox(height: 16.h),
                
                // اختيار الدور
                DropdownButtonFormField<String>(
                  value: selectedRole,
                  decoration: InputDecoration(
                    labelText: tr('role'),
                    prefixIcon: const Icon(Iconsax.user_tag),
                  ),
                  items: UserRoles.getAvailableRoles(_currentUserRole)
                      .map((role) => DropdownMenuItem(
                            value: role,
                            child: Text(_getRoleTranslation(role)),
                          ))
                      .toList(),
                  onChanged: (value) {
                    if (value != null) {
                      selectedRole = value;
                    }
                  },
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(tr('cancel')),
          ),
          ElevatedButton(
            onPressed: () async {
              if (formKey.currentState!.validate()) {
                Navigator.of(context).pop();
                
                // عرض مؤشر التحميل
                setState(() {
                  _isLoading = true;
                });
                
                try {
                  // تحديث بيانات المستخدم
                  final success = await AdminService.updateUserProfile(
                    user.id,
                    {
                      'full_name': nameController.text.trim(),
                      'phone': phoneController.text.trim(),
                      'role': selectedRole,
                    },
                  );
                  
                  if (success && mounted) {
                    // إعادة تحميل البيانات
                    await _loadData();
                    
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text(tr('user_updated_successfully'))),
                    );
                  } else if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text(tr('failed_to_update_user'))),
                    );
                    setState(() {
                      _isLoading = false;
                    });
                  }
                } catch (e) {
                  if (mounted) {
                    setState(() {
                      _isLoading = false;
                    });
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('خطأ في تحديث بيانات المستخدم: ${e.toString()}')),
                    );
                  }
                }
              }
            },
            child: Text(tr('save')),
          ),
        ],
      ),
    );
  }

  Color _getRoleColor(String role) {
    switch (role) {
      case 'admin':
        return Colors.red;
      case 'manager':
        return Colors.orange;
      case 'operator':
        return Colors.blue;
      default:
        return Colors.green;
    }
  }

  String _getRoleTranslation(String role) {
    switch (role) {
      case 'admin':
        return tr('role_admin');
      case 'manager':
        return tr('role_manager');
      case 'operator':
        return tr('role_operator');
      default:
        return tr('role_user');
    }
  }
}

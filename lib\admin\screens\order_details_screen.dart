import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:iconsax/iconsax.dart';
import '../services/admin_service.dart';
import '../widgets/order_tracking_history.dart';
import '../widgets/order_address_card.dart';
import '../widgets/order_status_editor.dart';
import '../models/admin_user_model.dart';

class OrderDetailsScreen extends StatefulWidget {
  final String orderId;
  final Map<String, dynamic> orderData;
  final bool isEditing;

  const OrderDetailsScreen({
    Key? key,
    required this.orderId,
    required this.orderData,
    this.isEditing = false,
  }) : super(key: key);

  @override
  State<OrderDetailsScreen> createState() => _OrderDetailsScreenState();
}

class _OrderDetailsScreenState extends State<OrderDetailsScreen> {
  bool _isLoading = false;
  Map<String, dynamic> _orderData = {};
  String _currentUserRole = '';

  // للتعديل
  final _formKey = GlobalKey<FormState>();
  final _quantityController = TextEditingController();
  final _notesController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _orderData = Map<String, dynamic>.from(widget.orderData);
    _loadUserRole();
    _initControllers();
  }

  @override
  void dispose() {
    _quantityController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  void _initControllers() {
    _quantityController.text = (_orderData['quantity'] ?? 0).toString();
    _notesController.text = _orderData['notes'] ?? '';
  }

  Future<void> _loadUserRole() async {
    try {
      final role = await AdminService.getCurrentUserRole();
      if (mounted) {
        setState(() {
          _currentUserRole = role;
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل دور المستخدم: $e');
    }
  }

  Future<void> _refreshOrderData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      debugPrint('🔄 تحديث بيانات الطلبية: ${widget.orderId}');

      // جلب بيانات الطلبية المحدثة مع بيانات التتبع
      final updatedOrder = await AdminService.getOrderById(widget.orderId);

      if (mounted) {
        if (updatedOrder != null) {
          setState(() {
            _orderData = updatedOrder;
            _isLoading = false;
          });
          debugPrint('✅ تم تحديث بيانات الطلبية بنجاح');
          debugPrint('الحالة الجديدة: ${updatedOrder['status']}');
          debugPrint('بيانات التتبع: ${updatedOrder['order_tracking']}');
        } else {
          setState(() {
            _isLoading = false;
          });
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('لم يتم العثور على الطلبية')));
        }
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحديث بيانات الطلبية: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث بيانات الطلبية: ${e.toString()}'),
          ),
        );
      }
    }
  }

  Future<void> _updateOrderDetails() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final updatedData = {
        'quantity':
            int.tryParse(_quantityController.text) ?? _orderData['quantity'],
        'notes': _notesController.text.trim(),
      };

      final success = await AdminService.updateOrder(
        widget.orderId,
        updatedData,
      );

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(tr('order_updated_successfully'))),
          );
          await _refreshOrderData();
        } else {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(tr('failed_to_update_order'))));
          setState(() {
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث بيانات الطلبية: ${e.toString()}'),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final orderId = _orderData['id'] ?? '';
    final orderStatus = _orderData['status'] ?? 'pending';
    final orderDate = _orderData['created_at'] != null
        ? DateFormat(
            'yyyy-MM-dd HH:mm',
          ).format(DateTime.parse(_orderData['created_at']))
        : tr('unknown');
    final customerName = _orderData['profiles'] != null
        ? _orderData['profiles']['full_name'] ?? tr('unknown')
        : tr('unknown');
    final customerPhone = _orderData['profiles'] != null
        ? _orderData['profiles']['phone'] ?? tr('not_provided')
        : tr('not_provided');
    final quantity = _orderData['quantity'] ?? 0;
    final totalAmount =
        _orderData['total_amount'] ?? _orderData['total_price'] ?? 0.0;
    final notes = _orderData['notes'] ?? '';

    // بيانات العنوان
    final addressData = _orderData['addresses'];

    // بيانات التتبع
    final trackingHistory = _orderData['order_tracking'] ?? [];

    return Scaffold(
      appBar: AppBar(
        title: Text(tr('order_details')),
        actions: [
          IconButton(
            icon: const Icon(Iconsax.refresh),
            onPressed: _refreshOrderData,
            tooltip: tr('refresh'),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // بطاقة معلومات الطلبية الأساسية
                  Card(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Padding(
                      padding: EdgeInsets.all(16.w),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                padding: EdgeInsets.all(12.w),
                                decoration: BoxDecoration(
                                  color: _getStatusColor(
                                    orderStatus,
                                  ).withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(12.r),
                                ),
                                child: Icon(
                                  _getStatusIcon(orderStatus),
                                  color: _getStatusColor(orderStatus),
                                  size: 24.r,
                                ),
                              ),
                              SizedBox(width: 16.w),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      '${tr('order_id')}: $orderId',
                                      style: TextStyle(
                                        fontSize: 16.sp,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    SizedBox(height: 4.h),
                                    Text(
                                      '${tr('date')}: $orderDate',
                                      style: TextStyle(
                                        fontSize: 14.sp,
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 16.h),
                          Divider(),
                          SizedBox(height: 16.h),

                          // معلومات العميل
                          Text(
                            tr('customer_info'),
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: 12.h),
                          _buildInfoRow(
                            icon: Iconsax.user,
                            label: tr('name'),
                            value: customerName,
                          ),
                          SizedBox(height: 8.h),
                          _buildInfoRow(
                            icon: Iconsax.call,
                            label: tr('phone'),
                            value: customerPhone,
                          ),
                          SizedBox(height: 16.h),
                          Divider(),
                          SizedBox(height: 16.h),

                          // معلومات الطلبية
                          Text(
                            tr('order_info'),
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: 12.h),

                          // إذا كان في وضع التعديل
                          widget.isEditing &&
                                  UserRoles.canManageOrders(_currentUserRole)
                              ? Form(
                                  key: _formKey,
                                  child: Column(
                                    children: [
                                      // تعديل الكمية
                                      TextFormField(
                                        controller: _quantityController,
                                        decoration: InputDecoration(
                                          labelText: tr('quantity'),
                                          prefixIcon: const Icon(Iconsax.box),
                                          border: OutlineInputBorder(
                                            borderRadius: BorderRadius.circular(
                                              12.r,
                                            ),
                                          ),
                                        ),
                                        keyboardType: TextInputType.number,
                                        validator: (value) {
                                          if (value == null || value.isEmpty) {
                                            return tr('quantity_required');
                                          }
                                          if (int.tryParse(value) == null) {
                                            return tr('invalid_quantity');
                                          }
                                          return null;
                                        },
                                      ),
                                      SizedBox(height: 16.h),

                                      // تعديل الملاحظات
                                      TextFormField(
                                        controller: _notesController,
                                        decoration: InputDecoration(
                                          labelText: tr('notes'),
                                          prefixIcon: const Icon(Iconsax.note),
                                          border: OutlineInputBorder(
                                            borderRadius: BorderRadius.circular(
                                              12.r,
                                            ),
                                          ),
                                        ),
                                        maxLines: 3,
                                      ),
                                      SizedBox(height: 16.h),

                                      // زر حفظ التغييرات
                                      SizedBox(
                                        width: double.infinity,
                                        child: ElevatedButton(
                                          onPressed: _updateOrderDetails,
                                          style: ElevatedButton.styleFrom(
                                            padding: EdgeInsets.symmetric(
                                              vertical: 12.h,
                                            ),
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(12.r),
                                            ),
                                          ),
                                          child: Text(
                                            tr('save_changes'),
                                            style: TextStyle(
                                              fontSize: 16.sp,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                              : Column(
                                  children: [
                                    _buildInfoRow(
                                      icon: Iconsax.box,
                                      label: tr('quantity'),
                                      value: quantity.toString(),
                                    ),
                                    SizedBox(height: 8.h),
                                    _buildInfoRow(
                                      icon: Iconsax.money,
                                      label: tr('total_amount'),
                                      value: '$totalAmount ${tr('currency')}',
                                    ),
                                    if (notes.isNotEmpty) ...[
                                      SizedBox(height: 8.h),
                                      _buildInfoRow(
                                        icon: Iconsax.note,
                                        label: tr('notes'),
                                        value: notes,
                                      ),
                                    ],
                                  ],
                                ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(height: 16.h),

                  // بطاقة العنوان
                  OrderAddressCard(addressData: addressData),
                  SizedBox(height: 16.h),

                  // تحديث حالة الطلبية (للمستخدمين المصرح لهم فقط)
                  if (UserRoles.canManageOrders(_currentUserRole))
                    OrderStatusEditor(
                      orderId: orderId,
                      currentStatus: orderStatus,
                      onStatusUpdated: _refreshOrderData,
                    ),
                  SizedBox(height: 16.h),

                  // سجل تتبع الطلبية
                  if (trackingHistory.isNotEmpty)
                    OrderTrackingHistory(trackingHistory: trackingHistory),
                ],
              ),
            ),
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 20.r, color: Colors.grey[600]),
        SizedBox(width: 12.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(fontSize: 14.sp, color: Colors.grey[600]),
              ),
              SizedBox(height: 4.h),
              Text(
                value,
                style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w500),
              ),
            ],
          ),
        ),
      ],
    );
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'pending':
        return Iconsax.receipt; // استلام الطلبية
      case 'processing':
        return Iconsax.document_text; // جاري المعالجة
      case 'shipping':
        return Iconsax.box; // جاري الشحن
      case 'out_for_delivery':
        return Iconsax.truck; // جاري التوصيل
      case 'delivered':
        return Iconsax.tick_circle; // تم التوزيع
      case 'cancelled':
        return Iconsax.close_circle; // ملغية
      default:
        return Iconsax.receipt;
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'pending':
        return Colors.orange; // استلام الطلبية
      case 'processing':
        return Colors.blue; // جاري المعالجة
      case 'shipping':
        return Colors.indigo; // جاري الشحن
      case 'out_for_delivery':
        return Colors.deepPurple; // جاري التوصيل
      case 'delivered':
        return Colors.green; // تم التوزيع
      case 'cancelled':
        return Colors.red; // ملغية
      default:
        return Colors.grey;
    }
  }
}

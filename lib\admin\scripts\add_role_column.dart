import 'package:flutter/material.dart';
import '../../services/supabase_service.dart';

/// سكريبت لإضافة عمود role إلى جدول profiles
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // تهيئة Supabase
  await SupabaseService.initialize();
  
  print('بدء عملية إضافة عمود role إلى جدول profiles...');
  
  try {
    // إضافة عمود role إلى جدول profiles
    final response = await SupabaseService.client.rpc(
      'add_role_column_to_profiles',
    );
    
    print('✅ تم إضافة عمود role إلى جدول profiles بنجاح');
    print('الاستجابة: $response');
    
    // إضافة المستخدم <EMAIL> كمسؤول
    print('جاري إضافة المستخدم <EMAIL> كمسؤول...');
    final result = await SupabaseService.updateUserRoleByEmail('<EMAIL>', 'admin');
    
    if (result) {
      print('✅ تم إضافة المستخدم <EMAIL> كمسؤول بنجاح');
    } else {
      print('❌ فشل إضافة المستخدم <EMAIL> كمسؤول');
    }
  } catch (e) {
    print('❌ حدث خطأ أثناء إضافة عمود role: $e');
  }
  
  print('انتهت العملية.');
}

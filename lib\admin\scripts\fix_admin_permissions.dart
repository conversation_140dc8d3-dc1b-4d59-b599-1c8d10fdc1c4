import 'package:flutter/material.dart';
import '../../services/supabase_service.dart';

/// سكريبت لإصلاح صلاحيات المسؤول
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  print('🔧 بدء إصلاح صلاحيات المسؤول...');
  
  try {
    // تهيئة Supabase
    await SupabaseService.initialize();
    print('✅ تم تهيئة Supabase بنجاح');
    
    // قائمة المستخدمين المسؤولين
    final adminEmails = [
      '<EMAIL>',
      // يمكن إضافة المزيد من البريد الإلكتروني للمسؤولين هنا
    ];
    
    for (final email in adminEmails) {
      print('🔄 معالجة المستخدم: $email');
      
      // إضافة المستخدم كمسؤول
      final result = await SupabaseService.updateUserRoleByEmail(email, 'admin');
      
      if (result) {
        print('✅ تم تحديث صلاحيات المستخدم $email إلى admin بنجاح');
      } else {
        print('❌ فشل تحديث صلاحيات المستخدم $email');
      }
    }
    
    // التحقق من جدول order_tracking
    print('🔄 التحقق من جدول order_tracking...');
    
    try {
      final trackingData = await SupabaseService.client
          .from('order_tracking')
          .select('*')
          .limit(1);
      
      print('✅ جدول order_tracking يعمل بشكل صحيح');
      print('عدد السجلات: ${trackingData.length}');
    } catch (e) {
      print('❌ مشكلة في جدول order_tracking: $e');
    }
    
    // التحقق من جدول orders
    print('🔄 التحقق من جدول orders...');
    
    try {
      final ordersData = await SupabaseService.client
          .from('orders')
          .select('id, status')
          .limit(5);
      
      print('✅ جدول orders يعمل بشكل صحيح');
      print('عدد الطلبيات: ${ordersData.length}');
      
      for (final order in ordersData) {
        print('  - طلبية ${order['id']}: ${order['status']}');
      }
    } catch (e) {
      print('❌ مشكلة في جدول orders: $e');
    }
    
    print('🎉 انتهت عملية إصلاح الصلاحيات');
    
  } catch (e) {
    print('❌ حدث خطأ أثناء إصلاح الصلاحيات: $e');
  }
}

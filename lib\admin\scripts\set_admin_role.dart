import 'package:flutter/material.dart';
import '../../services/supabase_service.dart';

/// Script para asignar el rol de administrador a un usuario específico
/// Ejecutar este script como una aplicación independiente para asignar el rol
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Inicializar Supabase
  await SupabaseService.initialize();
  
  // Correo electrónico del usuario al que se le asignará el rol de administrador
  const String adminEmail = '<EMAIL>';
  
  print('Iniciando proceso para asignar rol de administrador a: $adminEmail');
  
  try {
    // Asignar rol de administrador
    final result = await SupabaseService.updateUserRoleByEmail(adminEmail, 'admin');
    
    if (result) {
      print('✅ Rol de administrador asignado correctamente a: $adminEmail');
    } else {
      print('❌ No se pudo asignar el rol de administrador. Verifique que el usuario exista.');
    }
  } catch (e) {
    print('❌ Error al asignar rol de administrador: $e');
  }
  
  // Salir de la aplicación
  print('Proceso completado.');
}

import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../services/supabase_service.dart';
import '../models/admin_user_model.dart';
import '../models/dashboard_stats_model.dart';

/// خدمة إدارة لوحة التحكم
class AdminService {
  /// الحصول على مرجع لعميل Supabase
  static SupabaseClient get _client => SupabaseService.client;

  /// التحقق مما إذا كان المستخدم الحالي يملك صلاحيات إدارية
  static Future<bool> isAdmin() async {
    try {
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) return false;

      final userData = await _client
          .from('profiles')
          .select('role')
          .eq('id', currentUser.id)
          .single();

      final role = userData['role'] as String?;
      return role != null && UserRoles.hasAdminAccess(role);
    } catch (e) {
      debugPrint('خطأ في التحقق من صلاحيات المستخدم: $e');
      return false;
    }
  }

  /// الحصول على دور المستخدم الحالي
  static Future<String> getCurrentUserRole() async {
    try {
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) return UserRoles.user;

      final userData = await _client
          .from('profiles')
          .select('role')
          .eq('id', currentUser.id)
          .single();

      return userData['role'] as String? ?? UserRoles.user;
    } catch (e) {
      debugPrint('خطأ في الحصول على دور المستخدم: $e');
      return UserRoles.user;
    }
  }

  /// الحصول على قائمة المستخدمين
  static Future<List<AdminUser>> getAllUsers() async {
    try {
      final response = await _client
          .from('profiles')
          .select('*')
          .order('created_at', ascending: false);

      return response.map((data) {
        return AdminUser.fromJson(data);
      }).toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على قائمة المستخدمين: $e');
      return [];
    }
  }

  /// تحديث دور المستخدم
  static Future<bool> updateUserRole(String userId, String newRole) async {
    try {
      await _client.from('profiles').update({'role': newRole}).eq('id', userId);
      return true;
    } catch (e) {
      debugPrint('خطأ في تحديث دور المستخدم: $e');
      return false;
    }
  }

  /// الحصول على إحصائيات لوحة التحكم
  static Future<DashboardStats> getDashboardStats() async {
    try {
      // الحصول على عدد المستخدمين
      final usersResponse = await _client.from('profiles').select('id');

      // الحصول على بيانات الطلبيات
      final orders = await _client
          .from('orders')
          .select('id, status, quantity, total_amount, total_price');

      // حساب الإحصائيات
      final totalUsers = usersResponse.length;
      final totalOrders = orders.length;

      // حساب عدد الطلبيات حسب الحالة
      final ordersByStatus = {
        'pending': 0,
        'processing': 0,
        'shipping': 0,
        'out_for_delivery': 0,
        'delivered': 0,
        'cancelled': 0,
      };

      int totalQuantity = 0;
      double totalAmount = 0.0;
      double completedDonationsAmount = 0.0;

      for (final order in orders) {
        // حساب عدد الطلبيات حسب الحالة
        final status = order['status'] as String? ?? 'pending';
        ordersByStatus[status] = (ordersByStatus[status] ?? 0) + 1;

        // حساب إجمالي الكميات
        totalQuantity += order['quantity'] as int? ?? 0;

        // حساب إجمالي المبالغ
        // نحاول استخدام total_amount أولاً، وإذا لم يكن متاحاً نستخدم total_price
        final amount = order['total_amount'] ?? order['total_price'] ?? 0;
        final amountValue = (amount is int)
            ? amount.toDouble()
            : (amount as double? ?? 0.0);

        totalAmount += amountValue;

        // حساب إجمالي التبرعات المكتملة (المدفوعة فقط)
        // نعتبر الطلبيات المكتملة والمسلمة كتبرعات مدفوعة
        if (status == 'delivered' || status == 'completed') {
          completedDonationsAmount += amountValue;
        }
      }

      return DashboardStats(
        totalUsers: totalUsers,
        totalOrders: totalOrders,
        ordersByStatus: ordersByStatus,
        totalQuantity: totalQuantity,
        totalAmount: totalAmount,
        completedDonationsAmount: completedDonationsAmount,
      );
    } catch (e) {
      debugPrint('خطأ في الحصول على إحصائيات لوحة التحكم: $e');
      return DashboardStats.empty();
    }
  }

  /// الحصول على قائمة الطلبيات
  static Future<List<Map<String, dynamic>>> getAllOrders() async {
    try {
      final response = await _client
          .from('orders')
          .select('''
            *,
            addresses(*),
            order_tracking(*)
          ''')
          .order('created_at', ascending: false);

      return response;
    } catch (e) {
      debugPrint('خطأ في الحصول على قائمة الطلبيات: $e');
      return [];
    }
  }

  /// تحديث حالة الطلبية
  static Future<bool> updateOrderStatus(
    String orderId,
    String newStatus,
    String notes,
  ) async {
    try {
      debugPrint('محاولة تحديث الطلبية: $orderId إلى الحالة: $newStatus');

      // تحديث حالة الطلبية في جدول orders
      await _client
          .from('orders')
          .update({'status': newStatus})
          .eq('id', orderId);

      debugPrint('تم تحديث حالة الطلبية في جدول orders بنجاح');

      // محاولة تحديث جدول order_tracking بطريقة مبسطة
      try {
        final now = DateTime.now().toIso8601String();

        // البحث عن سجل التتبع الموجود
        final existingTracking = await _client
            .from('order_tracking')
            .select('*')
            .eq('order_id', orderId)
            .maybeSingle();

        Map<String, dynamic> trackingData = {
          'order_id': orderId,
          'status': newStatus,
          'updated_at': now,
        };

        if (notes.isNotEmpty) {
          trackingData['notes'] = notes;
        }

        if (existingTracking != null) {
          // تحديث السجل الموجود
          debugPrint('تحديث سجل التتبع الموجود');
          await _client
              .from('order_tracking')
              .update(trackingData)
              .eq('order_id', orderId);
        } else {
          // إنشاء سجل تتبع جديد
          debugPrint('إنشاء سجل تتبع جديد');
          trackingData['created_at'] = now;
          await _client.from('order_tracking').insert(trackingData);
        }

        debugPrint('تم تحديث جدول order_tracking بنجاح');
      } catch (trackingError) {
        debugPrint('خطأ في تحديث جدول order_tracking: $trackingError');
        // لا نفشل العملية إذا فشل تحديث التتبع
      }

      return true;
    } catch (e) {
      debugPrint('خطأ في تحديث حالة الطلبية: $e');
      return false;
    }
  }

  /// تحديث بيانات الطلبية
  static Future<bool> updateOrder(
    String orderId,
    Map<String, dynamic> orderData,
  ) async {
    try {
      await _client.from('orders').update(orderData).eq('id', orderId);

      return true;
    } catch (e) {
      debugPrint('خطأ في تحديث بيانات الطلبية: $e');
      return false;
    }
  }

  /// تحديث بيانات المستخدم
  static Future<bool> updateUserProfile(
    String userId,
    Map<String, dynamic> userData,
  ) async {
    try {
      await _client.from('profiles').update(userData).eq('id', userId);

      return true;
    } catch (e) {
      debugPrint('خطأ في تحديث بيانات المستخدم: $e');
      return false;
    }
  }
}

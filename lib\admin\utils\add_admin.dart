import 'package:flutter/material.dart';
import '../../services/supabase_service.dart';

/// أداة مساعدة لإضافة مستخدم كمسؤول
class AddAdminTool {
  /// إضافة مستخدم كمسؤول عن طريق البريد الإلكتروني
  static Future<bool> addAdmin(String email) async {
    try {
      // تهيئة Supabase إذا لم يكن مهيأ بالفعل
      await SupabaseService.initialize();
      
      // تحديث دور المستخدم إلى مسؤول
      final result = await SupabaseService.updateUserRoleByEmail(email, 'admin');
      
      if (result) {
        print('✅ تم إضافة المستخدم $email كمسؤول بنجاح');
      } else {
        print('❌ فشل إضافة المستخدم $email كمسؤول');
      }
      
      return result;
    } catch (e) {
      print('❌ حدث خطأ أثناء إضافة المسؤول: $e');
      return false;
    }
  }
}

/// دالة رئيسية لتنفيذ إضافة المسؤول
Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  final email = '<EMAIL>';
  final result = await AddAdminTool.addAdmin(email);
  
  print('نتيجة العملية: ${result ? "ناجحة" : "فاشلة"}');
  
  // إنهاء التطبيق بعد الانتهاء
  // ignore: avoid_print
  print('تم الانتهاء من العملية، يمكنك إغلاق هذا التطبيق.');
}

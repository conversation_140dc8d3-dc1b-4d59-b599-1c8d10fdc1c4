import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:iconsax/iconsax.dart';

class AdminSidebar extends StatelessWidget {
  final int selectedIndex;
  final Function(int) onItemSelected;

  const AdminSidebar({
    Key? key,
    required this.selectedIndex,
    required this.onItemSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Container(
      width: 240.w,
      color: isDarkMode ? const Color(0xFF1E2233) : Colors.white,
      child: Column(
        children: [
          // لوجو التطبيق والعنوان
          Container(
            padding: EdgeInsets.symmetric(vertical: 24.h),
            alignment: Alignment.center,
            child: Column(
              children: [
                // لوجو التطبيق (استبدلناه بأيقونة)
                CircleAvatar(
                  radius: 32.r,
                  backgroundColor: Theme.of(context).primaryColor.withOpacity(0.2),
                  child: Icon(
                    Icons.water_drop,
                    size: 32.r,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                SizedBox(height: 12.h),
                // اسم التطبيق
                Text(
                  tr('app_name'),
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 4.h),
                // عنوان لوحة التحكم
                Text(
                  tr('admin_panel'),
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          
          const Divider(),
          
          // عناصر القائمة
          Expanded(
            child: ListView(
              padding: EdgeInsets.symmetric(vertical: 8.h),
              children: [
                _buildNavItem(
                  context: context,
                  index: 0,
                  title: tr('dashboard'),
                  icon: Iconsax.home,
                  isSelected: selectedIndex == 0,
                ),
                _buildNavItem(
                  context: context,
                  index: 1,
                  title: tr('users'),
                  icon: Iconsax.user,
                  isSelected: selectedIndex == 1,
                ),
                _buildNavItem(
                  context: context,
                  index: 2,
                  title: tr('admin_orders'),
                  icon: Iconsax.shopping_cart,
                  isSelected: selectedIndex == 2,
                ),
                _buildNavItem(
                  context: context,
                  index: 3,
                  title: tr('admin_settings'),
                  icon: Iconsax.setting,
                  isSelected: selectedIndex == 3,
                ),
              ],
            ),
          ),
          
          const Divider(),
          
          // زر تسجيل الخروج
          Padding(
            padding: EdgeInsets.all(16.w),
            child: InkWell(
              onTap: () {
                // عرض مربع حوار لتأكيد تسجيل الخروج
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: Text(tr('logout')),
                    content: Text(tr('confirm_logout')),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: Text(tr('no')),
                      ),
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                          // استدعاء دالة تسجيل الخروج من الشاشة الرئيسية
                          onItemSelected(-1);
                        },
                        child: Text(tr('yes')),
                      ),
                    ],
                  ),
                );
              },
              child: Container(
                padding: EdgeInsets.symmetric(vertical: 12.h),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8.r),
                  color: isDarkMode ? Colors.red.withOpacity(0.2) : Colors.red.withOpacity(0.1),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Iconsax.logout,
                      color: Colors.red,
                      size: 20.r,
                    ),
                    SizedBox(width: 8.w),
                    Text(
                      tr('logout'),
                      style: TextStyle(
                        color: Colors.red,
                        fontWeight: FontWeight.w500,
                        fontSize: 14.sp,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem({
    required BuildContext context,
    required int index,
    required String title,
    required IconData icon,
    required bool isSelected,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).primaryColor;
    
    return ListTile(
      leading: Icon(
        icon,
        color: isSelected 
            ? primaryColor 
            : isDarkMode ? Colors.grey[400] : Colors.grey[700],
        size: 22.r,
      ),
      title: Text(
        title,
        style: TextStyle(
          fontSize: 15.sp,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          color: isSelected 
              ? primaryColor 
              : isDarkMode ? Colors.grey[300] : Colors.grey[800],
        ),
        textAlign: TextAlign.right,
      ),
      selected: isSelected,
      selectedTileColor: primaryColor.withOpacity(0.1),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.horizontal(
          left: Radius.circular(24.r),
        ),
      ),
      onTap: () => onItemSelected(index),
    );
  }
}

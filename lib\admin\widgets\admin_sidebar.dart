import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:iconsax/iconsax.dart';

class AdminSidebar extends StatelessWidget {
  final int selectedIndex;
  final Function(int) onItemSelected;

  const AdminSidebar({
    Key? key,
    required this.selectedIndex,
    required this.onItemSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return LayoutBuilder(
      builder: (context, constraints) {
        // تحديد عرض الشريط الجانبي حسب حجم الشاشة
        double sidebarWidth;
        bool isCompact = false;
        
        if (MediaQuery.of(context).size.width < 768) {
          sidebarWidth = 60.w; // شريط جانبي مضغوط للشاشات الصغيرة
          isCompact = true;
        } else if (MediaQuery.of(context).size.width < 1024) {
          sidebarWidth = 200.w; // عرض متوسط للشاشات المتوسطة
        } else {
          sidebarWidth = 280.w; // العرض الكامل للشاشات الكبيرة
        }

        return Container(
          width: sidebarWidth,
          decoration: BoxDecoration(
            color: isDarkMode ? const Color(0xFF1E2233) : const Color(0xFF2C3E50),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(2, 0),
              ),
            ],
          ),
          child: Column(
            children: [
              // لوجو التطبيق والعنوان
              Container(
                padding: EdgeInsets.symmetric(vertical: isCompact ? 16.h : 32.h),
                alignment: Alignment.center,
                child: Column(
                  children: [
                    // لوجو التطبيق
                    Container(
                      padding: EdgeInsets.all(isCompact ? 8.w : 16.w),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(isCompact ? 12.r : 20.r),
                      ),
                      child: Icon(
                        Icons.water_drop,
                        size: isCompact ? 24.r : 40.r,
                        color: Colors.white,
                      ),
                    ),
                    if (!isCompact) ...[
                      SizedBox(height: 16.h),
                      // اسم التطبيق
                      Text(
                        tr('app_name'),
                        style: TextStyle(
                          fontSize: 20.sp,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      // عنوان لوحة التحكم
                      Text(
                        tr('admin_panel'),
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                        ),
                      ),
                    ],
                  ],
                ),
              ),

              const Divider(),

              // عناصر القائمة
              Expanded(
                child: ListView(
                  padding: EdgeInsets.symmetric(vertical: 8.h),
                  children: [
                    _buildNavItem(
                      context: context,
                      index: 0,
                      title: tr('dashboard'),
                      icon: Iconsax.home,
                      isSelected: selectedIndex == 0,
                      isCompact: isCompact,
                    ),
                    _buildNavItem(
                      context: context,
                      index: 1,
                      title: tr('users'),
                      icon: Iconsax.user,
                      isSelected: selectedIndex == 1,
                      isCompact: isCompact,
                    ),
                    _buildNavItem(
                      context: context,
                      index: 2,
                      title: tr('admin_orders'),
                      icon: Iconsax.shopping_cart,
                      isSelected: selectedIndex == 2,
                      isCompact: isCompact,
                    ),
                    _buildNavItem(
                      context: context,
                      index: 3,
                      title: tr('admin_settings'),
                      icon: Iconsax.setting,
                      isSelected: selectedIndex == 3,
                      isCompact: isCompact,
                    ),
                  ],
                ),
              ),

              const Divider(),

              // زر تسجيل الخروج
              Padding(
                padding: EdgeInsets.all(isCompact ? 8.w : 16.w),
                child: InkWell(
                  onTap: () {
                    // عرض مربع حوار لتأكيد تسجيل الخروج
                    showDialog(
                      context: context,
                      builder: (context) => AlertDialog(
                        title: Text(tr('logout')),
                        content: Text(tr('confirm_logout')),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(),
                            child: Text(tr('no')),
                          ),
                          TextButton(
                            onPressed: () {
                              Navigator.of(context).pop();
                              // استدعاء دالة تسجيل الخروج من الشاشة الرئيسية
                              onItemSelected(-1);
                            },
                            child: Text(tr('yes')),
                          ),
                        ],
                      ),
                    );
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 12.h),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.r),
                      color: Colors.red.withValues(alpha: 0.1),
                      border: Border.all(
                        color: Colors.red.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: isCompact 
                      ? Icon(Iconsax.logout, color: Colors.red, size: 20.r)
                      : Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Iconsax.logout, color: Colors.red, size: 20.r),
                            SizedBox(width: 8.w),
                            Text(
                              tr('logout'),
                              style: TextStyle(
                                color: Colors.red,
                                fontWeight: FontWeight.w500,
                                fontSize: 14.sp,
                              ),
                            ),
                          ],
                        ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildNavItem({
    required BuildContext context,
    required int index,
    required String title,
    required IconData icon,
    required bool isSelected,
    required bool isCompact,
  }) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: isCompact ? 4.w : 12.w, vertical: 4.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.r),
        color: isSelected
            ? Colors.white.withValues(alpha: 0.1)
            : Colors.transparent,
      ),
      child: isCompact 
        ? Tooltip(
            message: title,
            child: InkWell(
              onTap: () => onItemSelected(index),
              borderRadius: BorderRadius.circular(12.r),
              child: Container(
                padding: EdgeInsets.all(12.w),
                child: Icon(
                  icon,
                  color: isSelected
                      ? Colors.white
                      : Colors.white.withValues(alpha: 0.7),
                  size: 24.r,
                ),
              ),
            ),
          )
        : ListTile(
            leading: Icon(
              icon,
              color: isSelected
                  ? Colors.white
                  : Colors.white.withValues(alpha: 0.7),
              size: 24.r,
            ),
            title: Text(
              title,
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                color: isSelected
                    ? Colors.white
                    : Colors.white.withValues(alpha: 0.8),
              ),
              textAlign: TextAlign.right,
            ),
            onTap: () => onItemSelected(index),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12.r),
            ),
          ),
    );
  }
}

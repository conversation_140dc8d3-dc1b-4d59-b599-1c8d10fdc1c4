import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:iconsax/iconsax.dart';

class OrderAddressCard extends StatelessWidget {
  final dynamic addressData;

  const OrderAddressCard({
    Key? key,
    required this.addressData,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (addressData == null) {
      return Card(
        margin: EdgeInsets.symmetric(vertical: 8.h),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Center(
            child: Text(
              tr('no_address_found'),
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[600],
              ),
            ),
          ),
        ),
      );
    }

    // استخراج بيانات العنوان
    final String addressText = _getAddressText(addressData);
    final String? city = _getAddressField(addressData, 'city');
    final String? district = _getAddressField(addressData, 'district');
    final String? street = _getAddressField(addressData, 'street');
    final String? building = _getAddressField(addressData, 'building');
    final String? floor = _getAddressField(addressData, 'floor');
    final String? apartment = _getAddressField(addressData, 'apartment');
    final String? notes = _getAddressField(addressData, 'notes');

    return Card(
      margin: EdgeInsets.symmetric(vertical: 8.h),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Iconsax.location,
                  color: Theme.of(context).primaryColor,
                  size: 24.r,
                ),
                SizedBox(width: 12.w),
                Text(
                  tr('delivery_address'),
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            
            // عنوان كامل
            if (addressText.isNotEmpty)
              _buildAddressRow(
                icon: Iconsax.location,
                title: tr('full_address'),
                value: addressText,
              ),
            
            // المدينة
            if (city != null && city.isNotEmpty)
              _buildAddressRow(
                icon: Iconsax.building,
                title: tr('city'),
                value: city,
              ),
            
            // الحي
            if (district != null && district.isNotEmpty)
              _buildAddressRow(
                icon: Iconsax.home,
                title: tr('district'),
                value: district,
              ),
            
            // الشارع
            if (street != null && street.isNotEmpty)
              _buildAddressRow(
                icon: Iconsax.routing,
                title: tr('street'),
                value: street,
              ),
            
            // المبنى
            if (building != null && building.isNotEmpty)
              _buildAddressRow(
                icon: Iconsax.building_3,
                title: tr('building'),
                value: building,
              ),
            
            // الطابق
            if (floor != null && floor.isNotEmpty)
              _buildAddressRow(
                icon: Iconsax.arrow_up,
                title: tr('floor'),
                value: floor,
              ),
            
            // الشقة
            if (apartment != null && apartment.isNotEmpty)
              _buildAddressRow(
                icon: Iconsax.key,
                title: tr('apartment'),
                value: apartment,
              ),
            
            // ملاحظات
            if (notes != null && notes.isNotEmpty)
              _buildAddressRow(
                icon: Iconsax.note,
                title: tr('notes'),
                value: notes,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildAddressRow({
    required IconData icon,
    required String title,
    required String value,
  }) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            size: 18.r,
            color: Colors.grey[600],
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.grey[600],
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getAddressText(dynamic addressData) {
    if (addressData == null) return '';
    
    // إذا كان العنوان نصاً مباشراً
    if (addressData is String) return addressData;
    
    // إذا كان العنوان قائمة
    if (addressData is List) {
      if (addressData.isEmpty) return '';
      
      // نفترض أن العنصر الأول هو العنوان الرئيسي
      final firstAddress = addressData[0];
      if (firstAddress is Map) {
        return _extractAddressFromMap(firstAddress);
      } else if (firstAddress is String) {
        return firstAddress;
      }
    }
    
    // إذا كان العنوان خريطة
    if (addressData is Map) {
      return _extractAddressFromMap(addressData);
    }
    
    return '';
  }

  String _extractAddressFromMap(Map addressMap) {
    // محاولة استخراج العنوان من الخريطة بناءً على الهيكل المتوقع
    final fullAddress = addressMap['full_address'] ?? addressMap['address'];
    if (fullAddress != null) return fullAddress.toString();
    
    // إذا لم يكن هناك عنوان كامل، نحاول بناء واحد من الحقول الفردية
    final List<String> addressParts = [];
    
    final city = addressMap['city'];
    final district = addressMap['district'];
    final street = addressMap['street'];
    final building = addressMap['building'];
    
    if (city != null && city.toString().isNotEmpty) addressParts.add(city.toString());
    if (district != null && district.toString().isNotEmpty) addressParts.add(district.toString());
    if (street != null && street.toString().isNotEmpty) addressParts.add(street.toString());
    if (building != null && building.toString().isNotEmpty) addressParts.add(building.toString());
    
    return addressParts.join(', ');
  }

  String? _getAddressField(dynamic addressData, String fieldName) {
    if (addressData == null) return null;
    
    // إذا كان العنوان خريطة
    if (addressData is Map) {
      final value = addressData[fieldName];
      return value != null ? value.toString() : null;
    }
    
    // إذا كان العنوان قائمة
    if (addressData is List && addressData.isNotEmpty) {
      final firstAddress = addressData[0];
      if (firstAddress is Map) {
        final value = firstAddress[fieldName];
        return value != null ? value.toString() : null;
      }
    }
    
    return null;
  }
}

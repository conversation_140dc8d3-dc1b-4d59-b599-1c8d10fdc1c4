import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:iconsax/iconsax.dart';
import '../services/admin_service.dart';
import '../models/admin_user_model.dart';

class OrderStatusEditor extends StatefulWidget {
  final String orderId;
  final String currentStatus;
  final Function() onStatusUpdated;

  const OrderStatusEditor({
    Key? key,
    required this.orderId,
    required this.currentStatus,
    required this.onStatusUpdated,
  }) : super(key: key);

  @override
  State<OrderStatusEditor> createState() => _OrderStatusEditorState();
}

class _OrderStatusEditorState extends State<OrderStatusEditor> {
  late String _selectedStatus;
  final TextEditingController _notesController = TextEditingController();
  bool _isUpdating = false;

  @override
  void initState() {
    super.initState();
    _selectedStatus = widget.currentStatus;
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _updateOrderStatus() async {
    if (_selectedStatus == widget.currentStatus &&
        _notesController.text.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(tr('no_changes_to_save'))));
      return;
    }

    setState(() {
      _isUpdating = true;
    });

    try {
      debugPrint(
        'بدء تحديث حالة الطلبية: ${widget.orderId} إلى $_selectedStatus',
      );

      // التحقق من صلاحيات المستخدم
      final userRole = await AdminService.getCurrentUserRole();
      debugPrint('دور المستخدم الحالي: $userRole');

      if (!UserRoles.canManageOrders(userRole)) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('ليس لديك صلاحية لتحديث الطلبيات')),
          );
        }
        return;
      }

      final success = await AdminService.updateOrderStatus(
        widget.orderId,
        _selectedStatus,
        _notesController.text.trim(),
      );

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(tr('status_updated_successfully'))),
          );
          _notesController.clear();
          widget.onStatusUpdated();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(tr('failed_to_update_status'))),
          );
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحديث حالة الطلبية: $e');
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ: ${e.toString()}')));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.symmetric(vertical: 8.h),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Iconsax.status_up,
                  color: Theme.of(context).primaryColor,
                  size: 24.r,
                ),
                SizedBox(width: 12.w),
                Text(
                  tr('update_order_status'),
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),

            // حالة الطلبية الحالية
            Row(
              children: [
                Text(
                  '${tr('current_status')}:',
                  style: TextStyle(fontSize: 14.sp, color: Colors.grey[700]),
                ),
                SizedBox(width: 8.w),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 12.w,
                    vertical: 4.h,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusColor(
                      widget.currentStatus,
                    ).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16.r),
                  ),
                  child: Text(
                    _getStatusTranslation(widget.currentStatus),
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.bold,
                      color: _getStatusColor(widget.currentStatus),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),

            // اختيار الحالة الجديدة
            DropdownButtonFormField<String>(
              value: _selectedStatus,
              decoration: InputDecoration(
                labelText: tr('new_status'),
                prefixIcon: const Icon(Iconsax.status_up),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
              items:
                  [
                        'pending',
                        'processing',
                        'shipping',
                        'out_for_delivery',
                        'delivered',
                        'cancelled',
                      ]
                      .map(
                        (status) => DropdownMenuItem(
                          value: status,
                          child: Text(_getStatusTranslation(status)),
                        ),
                      )
                      .toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedStatus = value;
                  });
                }
              },
            ),
            SizedBox(height: 16.h),

            // ملاحظات التحديث
            TextFormField(
              controller: _notesController,
              decoration: InputDecoration(
                labelText: tr('status_update_notes'),
                prefixIcon: const Icon(Iconsax.note),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
              maxLines: 3,
            ),
            SizedBox(height: 16.h),

            // زر تحديث الحالة
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isUpdating ? null : _updateOrderStatus,
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(vertical: 12.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                ),
                child: _isUpdating
                    ? SizedBox(
                        width: 24.w,
                        height: 24.h,
                        child: CircularProgressIndicator(
                          strokeWidth: 2.w,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                    : Text(
                        tr('update_status'),
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'pending':
        return Colors.orange; // استلام الطلبية
      case 'processing':
        return Colors.blue; // جاري المعالجة
      case 'shipping':
        return Colors.indigo; // جاري الشحن
      case 'out_for_delivery':
        return Colors.deepPurple; // جاري التوصيل
      case 'delivered':
        return Colors.green; // تم التوزيع
      case 'cancelled':
        return Colors.red; // ملغية
      default:
        return Colors.grey;
    }
  }

  String _getStatusTranslation(String status) {
    switch (status) {
      case 'pending':
        return tr('pending'); // قيد الانتظار
      case 'processing':
        return tr('processing'); // جاري المعالجة
      case 'shipping':
        return tr('shipping'); // جاري الشحن
      case 'out_for_delivery':
        return tr('out_for_delivery'); // جاري التوصيل
      case 'delivered':
        return tr('delivered'); // تم التوزيع
      case 'cancelled':
        return tr('cancelled'); // ملغية
      default:
        return tr('unknown');
    }
  }
}

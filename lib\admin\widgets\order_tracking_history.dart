import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:iconsax/iconsax.dart';

class OrderTrackingHistory extends StatelessWidget {
  final List<dynamic> trackingHistory;

  const OrderTrackingHistory({
    Key? key,
    required this.trackingHistory,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // ترتيب سجلات التتبع بشكل تنازلي حسب التاريخ
    final sortedHistory = List<dynamic>.from(trackingHistory)
      ..sort((a, b) {
        final dateA = DateTime.parse(a['created_at']);
        final dateB = DateTime.parse(b['created_at']);
        return dateB.compareTo(dateA);
      });

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Text(
            tr('tracking_history'),
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        <PERSON><PERSON><PERSON><PERSON>(height: 16.h),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: sortedHistory.length,
          itemBuilder: (context, index) {
            final trackingItem = sortedHistory[index];
            final status = trackingItem['status'] ?? 'pending';
            final notes = trackingItem['notes'] ?? '';
            final date = DateTime.parse(trackingItem['created_at']);
            final formattedDate = DateFormat('yyyy-MM-dd HH:mm').format(date);

            return Container(
              margin: EdgeInsets.only(bottom: 16.h),
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: _getStatusColor(status).withOpacity(0.05),
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(
                  color: _getStatusColor(status).withOpacity(0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        _getStatusIcon(status),
                        color: _getStatusColor(status),
                        size: 24.r,
                      ),
                      SizedBox(width: 12.w),
                      Text(
                        _getStatusTranslation(status),
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                          color: _getStatusColor(status),
                        ),
                      ),
                      const Spacer(),
                      Text(
                        formattedDate,
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                  if (notes.isNotEmpty) ...[
                    SizedBox(height: 12.h),
                    Text(
                      notes,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.grey[700],
                      ),
                    ),
                  ],
                ],
              ),
            );
          },
        ),
      ],
    );
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'pending':
        return Iconsax.timer;
      case 'processing':
        return Iconsax.setting;
      case 'delivering':
        return Iconsax.truck;
      case 'delivered':
        return Iconsax.tick_circle;
      case 'cancelled':
        return Iconsax.close_circle;
      default:
        return Iconsax.timer;
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'pending':
        return Colors.amber;
      case 'processing':
        return Colors.blue;
      case 'delivering':
        return Colors.orange;
      case 'delivered':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getStatusTranslation(String status) {
    switch (status) {
      case 'pending':
        return tr('pending');
      case 'processing':
        return tr('processing');
      case 'delivering':
        return tr('delivering');
      case 'delivered':
        return tr('delivered');
      case 'cancelled':
        return tr('cancelled');
      default:
        return tr('unknown');
    }
  }
}

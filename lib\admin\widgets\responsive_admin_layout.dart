import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'admin_sidebar.dart';

class ResponsiveAdminLayout extends StatelessWidget {
  final int selectedIndex;
  final Function(int) onItemSelected;
  final Widget child;

  const ResponsiveAdminLayout({
    Key? key,
    required this.selectedIndex,
    required this.onItemSelected,
    required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // تحديد ما إذا كانت الشاشة صغيرة
        bool isSmallScreen = constraints.maxWidth < 768;
        
        if (isSmallScreen) {
          // للشاشات الصغيرة: استخدام Drawer
          return Scaffold(
            drawer: Drawer(
              child: AdminSidebar(
                selectedIndex: selectedIndex,
                onItemSelected: onItemSelected,
              ),
            ),
            body: child,
          );
        } else {
          // للشاشات الكبيرة: استخدام Row
          return Row(
            children: [
              AdminSidebar(
                selectedIndex: selectedIndex,
                onItemSelected: onItemSelected,
              ),
              Expanded(
                child: Container(
                  color: const Color(0xFFF5F5F5),
                  child: child,
                ),
              ),
            ],
          );
        }
      },
    );
  }
}

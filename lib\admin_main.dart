import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'services/supabase_service.dart';
import 'admin/admin_app.dart';

/// نقطة الدخول الرئيسية لتطبيق لوحة تحكم المسؤول
/// يمكن استخدام هذا الملف لتشغيل تطبيق لوحة التحكم كتطبيق مستقل
void main() async {
  // تأكد من تهيئة Flutter
  WidgetsFlutterBinding.ensureInitialized();
  
  // تهيئة الترجمة
  await EasyLocalization.ensureInitialized();
  
  // تهيئة Supabase
  await SupabaseService.initialize();

  // تشغيل التطبيق
  runApp(
    EasyLocalization(
      supportedLocales: const [Locale('ar'), Locale('en')],
      path: 'assets/translations',
      fallbackLocale: const Locale('ar'),
      child: const <PERSON><PERSON><PERSON><PERSON>(),
    ),
  );
}

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:iconsax/iconsax.dart';
import 'package:curved_navigation_bar/curved_navigation_bar.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:soqia_joud/screens/login_screen.dart';
import 'package:soqia_joud/screens/new_order_screen.dart';
import 'package:soqia_joud/screens/profile_screen.dart';
import 'package:soqia_joud/screens/order_tracking_screen.dart';
import 'package:soqia_joud/screens/address_book_screen.dart';
import 'package:soqia_joud/screens/my_orders_screen.dart';
import 'package:soqia_joud/services/supabase_service.dart';
import 'package:soqia_joud/widgets/app_drawer.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await EasyLocalization.ensureInitialized();
  
  await SupabaseService.initialize();

  runApp(
    EasyLocalization(
      supportedLocales: const [Locale('ar'), Locale('en')],
      path: 'assets/translations',
      fallbackLocale: const Locale('ar'),
      useOnlyLangCode: true,
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 812),
      minTextAdapt: true,
      builder: (context, child) => MaterialApp(
        debugShowCheckedModeBanner: false,
        title: 'سقيا جود',
        theme: ThemeData(
          colorScheme: ColorScheme.light(
            primary: Colors.green.shade700,
            secondary: Colors.orange.shade700,
          ),
          scaffoldBackgroundColor: Colors.white,
          appBarTheme: const AppBarTheme(
            backgroundColor: Colors.white,
            foregroundColor: Colors.black,
            elevation: 0,
          ),
        ),
        darkTheme: ThemeData(
          brightness: Brightness.dark,
          colorScheme: ColorScheme.dark(
            primary: Colors.green.shade400,
            secondary: Colors.orange.shade400,
          ),
          scaffoldBackgroundColor: Colors.grey.shade900,
          appBarTheme: AppBarTheme(
            backgroundColor: Colors.grey.shade900,
            foregroundColor: Colors.white,
            elevation: 0,
          ),
        ),
        themeMode: ThemeMode.system,
        localizationsDelegates: context.localizationDelegates,
        supportedLocales: context.supportedLocales,
        locale: context.locale,
        routes: {
          '/': (context) => FutureBuilder<bool>(
            future: SupabaseService.isAuthenticated(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Scaffold(
                  body: Center(
                    child: CircularProgressIndicator(),
                  ),
                );
              }
              
              final isAuthenticated = snapshot.data ?? false;
              return isAuthenticated ? const MyHomePage() : const LoginScreen();
            },
          ),
          '/login': (context) => const LoginScreen(),
          '/home': (context) => const MyHomePage(),
          '/new-order': (context) => const NewOrderScreen(),
          '/profile': (context) => const ProfileScreen(),
          '/order-tracking': (context) => const OrderTrackingScreen(),
          '/my-orders': (context) => const MyOrdersScreen(),
        },
      ),
    );
  }
}

class SplashScreen extends StatelessWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key});

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> with SingleTickerProviderStateMixin {
  int _page = 0;
  final GlobalKey<CurvedNavigationBarState> _bottomNavigationKey = GlobalKey();
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  late AnimationController _animationController;
  ThemeMode _themeMode = ThemeMode.system;
  
  final List<Widget> _screens = [
    const HomeContent(),
    const MyOrdersScreen(),
    const AddressBookScreen(),
    const ProfileScreen(),
  ];
  
  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
  }
  
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _themeMode = Theme.of(context).brightness == Brightness.dark ? ThemeMode.dark : ThemeMode.light;
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
  
  void _toggleDrawer() {
    if (_scaffoldKey.currentState!.isDrawerOpen) {
      _scaffoldKey.currentState!.closeDrawer();
      _animationController.reverse();
    } else {
      _scaffoldKey.currentState!.openDrawer();
      _animationController.forward();
    }
  }
  
  void _changeTheme(ThemeMode themeMode) {
    setState(() {
      _themeMode = themeMode;
    });
  }
  
  void _changeLanguage(Locale locale) {
    context.setLocale(locale);
  }
  
  @override
  Widget build(BuildContext context) {
    final secondaryColor = Theme.of(context).colorScheme.secondary;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Scaffold(
      key: _scaffoldKey,
      drawer: AppDrawer(
        onThemeChanged: _changeTheme,
        onLanguageChanged: _changeLanguage,
        currentThemeMode: _themeMode,
      ),
      appBar: AppBar(
        title: Text(tr('app_name'), 
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 22.sp,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: AnimatedIcon(
            icon: AnimatedIcons.menu_close,
            progress: _animationController,
          ),
          onPressed: _toggleDrawer,
        ),
        actions: [
          IconButton(
            icon: const Icon(Iconsax.notification),
            onPressed: () {
              // TODO: Navigate to notifications
            },
          ),
        ],
      ),
      body: _screens[_page],
      bottomNavigationBar: CurvedNavigationBar(
        key: _bottomNavigationKey,
        index: _page,
        height: 60.0,
        items: <Widget>[
          Icon(Iconsax.home, size: 30, color: isDarkMode ? Colors.white : Colors.black),
          Icon(Iconsax.shopping_cart, size: 30, color: isDarkMode ? Colors.white : Colors.black),
          Icon(Iconsax.location, size: 30, color: isDarkMode ? Colors.white : Colors.black),
          Icon(Iconsax.profile_circle, size: 30, color: isDarkMode ? Colors.white : Colors.black),
        ],
        color: isDarkMode ? Colors.grey.shade800 : Colors.white,
        buttonBackgroundColor: secondaryColor,
        backgroundColor: isDarkMode ? Colors.grey.shade900 : Colors.grey.shade100,
        animationCurve: Curves.easeInOut,
        animationDuration: const Duration(milliseconds: 400),
        onTap: (index) {
          setState(() {
            _page = index;
          });
        },
        letIndexChange: (index) => true,
      ),
    );
  }
}

class HomeContent extends StatelessWidget {
  const HomeContent({super.key});

  @override
  Widget build(BuildContext context) {
    final primaryColor = Theme.of(context).colorScheme.primary;
    
    return Center(
      child: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'مرحباً بك في سقيا جود',
              style: TextStyle(fontSize: 24.sp, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ).animate()
              .fadeIn(duration: const Duration(milliseconds: 500))
              .then(delay: const Duration(milliseconds: 200))
              .slide(begin: const Offset(0, -0.2), curve: Curves.easeOutQuad),
            
            SizedBox(height: 40.h),
            
            _buildActionButton(
              context: context,
              icon: Iconsax.shopping_cart,
              title: 'طلبية جديدة',
              color: primaryColor,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const NewOrderScreen()),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildActionButton({
    required BuildContext context,
    required IconData icon,
    required String title,
    required Color color,
    required VoidCallback onTap,
    int delay = 200,
  }) {
    return Container(
      height: 120.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: color,
        borderRadius: BorderRadius.circular(16),
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          splashColor: Colors.white.withOpacity(0.3),
          highlightColor: Colors.white.withOpacity(0.1),
          onTap: onTap,
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  icon,
                  size: 36.sp,
                  color: Colors.white,
                ),
                SizedBox(height: 8.h),
                Text(
                  title,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    ).animate()
      .fadeIn(duration: const Duration(milliseconds: 400))
      .then(delay: Duration(milliseconds: delay))
      .scale(begin: const Offset(0.8, 0.8), curve: Curves.easeOutBack);
  }
}

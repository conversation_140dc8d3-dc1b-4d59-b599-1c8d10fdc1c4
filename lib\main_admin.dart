import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:provider/provider.dart';
import 'services/supabase_service.dart';
import 'admin/admin_app.dart';

/// نقطة دخول تطبيق لوحة التحكم الإدارية
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // تهيئة EasyLocalization
  await EasyLocalization.ensureInitialized();
  
  // تهيئة Supabase
  await SupabaseService.initialize();
  
  runApp(
    EasyLocalization(
      supportedLocales: const [
        Locale('ar'),
        Locale('en'),
      ],
      path: 'assets/translations',
      fallbackLocale: const Locale('ar'),
      startLocale: const Locale('ar'),
      child: const AdminDashboardApp(),
    ),
  );
}

/// تطبيق لوحة التحكم الإدارية
class AdminDashboardApp extends StatelessWidget {
  const AdminDashboardApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(1366, 768), // حجم شاشة سطح المكتب
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) => const AdminApp(),
    );
  }
}

class Address {
  final String id;
  final String addressName;
  final String emirate;
  final String area;
  final String street;
  final String? buildingNumber;
  final String? floorNumber;
  final String? apartmentNumber;
  final String? landmark;
  final String? phone;
  final bool isDefault;

  Address({
    required this.id,
    required this.addressName,
    required this.emirate,
    required this.area,
    required this.street,
    this.buildingNumber,
    this.floorNumber,
    this.apartmentNumber,
    this.landmark,
    this.phone,
    this.isDefault = false,
  });

  factory Address.fromJson(Map<String, dynamic> json) {
    return Address(
      id: json['id'].toString(),
      addressName: json['address_name'] ?? '',
      emirate: json['emirate'] ?? '',
      area: json['area'] ?? '',
      street: json['street'] ?? '',
      buildingNumber: json['building_number'],
      floorNumber: json['floor_number'],
      apartmentNumber: json['apartment_number'],
      landmark: json['landmark'],
      phone: json['phone'],
      isDefault: json['is_default'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'address_name': addressName,
      'emirate': emirate,
      'area': area,
      'street': street,
      'building_number': buildingNumber,
      'floor_number': floorNumber,
      'apartment_number': apartmentNumber,
      'landmark': landmark,
      'phone': phone,
      'is_default': isDefault,
    };
  }
}

import 'package:soqia_joud/models/address.dart';

class Order {
  final int? id;
  final int quantity; // العودة إلى استخدام quantity
  final double unitPrice;
  final double totalPrice;
  final Address address;
  final String status; // pending, processing, delivering, delivered, cancelled
  final DateTime orderDate;

  Order({
    this.id,
    required this.quantity, // العودة إلى استخدام quantity
    required this.unitPrice,
    required this.totalPrice,
    required this.address,
    required this.status,
    required this.orderDate,
  });

  factory Order.fromJson(Map<String, dynamic> json) {
    return Order(
      id: json['id'],
      quantity: json['quantity'] ?? 0, // العودة إلى استخدام quantity
      unitPrice: json['unit_price'] ?? json['unitPrice'],
      totalPrice: json['total_price'] ?? json['totalPrice'],
      address: json['address'] is Map
          ? Address.fromJson(json['address'])
          : Address(
              id: json['address_id']?.toString() ?? '',
              addressName: json['address_name'] ?? '',
              emirate: json['emirate'] ?? '',
              area: json['area'] ?? '',
              street: json['street'] ?? '',
              buildingNumber: json['building_number'],
              floorNumber: json['floor_number'],
              apartmentNumber: json['apartment_number'],
              landmark: json['landmark'],
              phone: json['phone'],
              isDefault: json['is_default'] ?? false,
            ),
      status: json['status'],
      orderDate: json['order_date'] != null
          ? DateTime.parse(json['order_date'])
          : DateTime.parse(json['orderDate']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      'quantity': quantity, // العودة إلى استخدام quantity
      'unit_price': unitPrice,
      'total_price': totalPrice,
      'address_id': address.id,
      'status': status,
      'order_date': orderDate.toIso8601String(),
    };
  }
}

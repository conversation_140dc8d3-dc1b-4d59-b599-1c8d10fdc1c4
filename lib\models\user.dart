class User {
  final String id;
  final String fullName;
  final String email;
  final String phone;
  final String emirate;
  final String address;
  final List<String> savedAddresses;

  User({
    required this.id,
    required this.fullName,
    required this.email,
    required this.phone,
    required this.emirate,
    required this.address,
    this.savedAddresses = const [],
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'].toString(),
      fullName: json['full_name'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'] ?? '',
      emirate: json['emirate'] ?? '',
      address: json['address'] ?? '',
      savedAddresses: List<String>.from(json['saved_addresses'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'full_name': fullName,
      'email': email,
      'phone': phone,
      'emirate': emirate,
      'address': address,
      'saved_addresses': savedAddresses,
    };
  }
}

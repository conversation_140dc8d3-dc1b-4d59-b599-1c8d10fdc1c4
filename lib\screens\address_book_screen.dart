import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:iconsax/iconsax.dart';
import 'package:soqia_joud/models/address.dart';
import 'package:soqia_joud/services/supabase_service.dart';

class AddressBookScreen extends StatefulWidget {
  final bool isSelecting;
  final Function(Address)? onAddressSelected;

  const AddressBookScreen({
    super.key, 
    this.isSelecting = false,
    this.onAddressSelected,
  });

  @override
  State<AddressBookScreen> createState() => _AddressBookScreenState();
}

class _AddressBookScreenState extends State<AddressBookScreen> {
  List<Address> _addresses = [];
  bool _isLoading = true;
  bool _isEditing = false;
  Address? _editingAddress;
  
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _areaController = TextEditingController();
  final TextEditingController _streetController = TextEditingController();
  final TextEditingController _buildingNumberController = TextEditingController();
  final TextEditingController _floorNumberController = TextEditingController();
  final TextEditingController _apartmentNumberController = TextEditingController();
  final TextEditingController _landmarkController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  bool _isDefault = false;
  
  final List<String> _emirates = [
    'أبو ظبي',
    'دبي',
    'الشارقة',
    'عجمان',
    'أم القيوين',
    'رأس الخيمة',
    'الفجيرة'
  ];
  
  String? _selectedEmirate;

  @override
  void initState() {
    super.initState();
    _loadAddresses();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _areaController.dispose();
    _streetController.dispose();
    _buildingNumberController.dispose();
    _floorNumberController.dispose();
    _apartmentNumberController.dispose();
    _landmarkController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  Future<void> _loadAddresses() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final addressesData = await SupabaseService.getUserAddresses();
      
      setState(() {
        _addresses = addressesData
            .map((item) => Address.fromJson(item))
            .toList();
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: ${e.toString()}')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // تم تحديث المتغيرات لتتوافق مع نموذج العنوان الجديد
  
  void _showAddEditAddressForm({Address? address}) {
    setState(() {
      _isEditing = true;
      _editingAddress = address;
      
      if (address != null) {
        _nameController.text = address.addressName;
        _selectedEmirate = address.emirate;
        _areaController.text = address.area;
        _streetController.text = address.street;
        _buildingNumberController.text = address.buildingNumber ?? '';
        _floorNumberController.text = address.floorNumber ?? '';
        _apartmentNumberController.text = address.apartmentNumber ?? '';
        _landmarkController.text = address.landmark ?? '';
        _phoneController.text = address.phone ?? '';
        _isDefault = address.isDefault;
      } else {
        _nameController.text = '';
        _selectedEmirate = null;
        _areaController.text = '';
        _streetController.text = '';
        _buildingNumberController.text = '';
        _floorNumberController.text = '';
        _apartmentNumberController.text = '';
        _landmarkController.text = '';
        _phoneController.text = '';
        _isDefault = false;
      }
    });
  }

  Future<void> _saveAddress() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      if (_editingAddress == null) {
        // Add new address
        await SupabaseService.addAddress(
          addressName: _nameController.text.trim(),
          emirate: _selectedEmirate!,
          area: _areaController.text.trim(),
          street: _streetController.text.trim(),
          buildingNumber: _buildingNumberController.text.trim(),
          floorNumber: _floorNumberController.text.trim(),
          apartmentNumber: _apartmentNumberController.text.trim(),
          landmark: _landmarkController.text.trim(),
          phone: _phoneController.text.trim(),
          isDefault: _isDefault,
        );
      } else {
        // Update existing address
        await SupabaseService.updateAddress(
          id: _editingAddress!.id,
          addressName: _nameController.text.trim(),
          emirate: _selectedEmirate!,
          area: _areaController.text.trim(),
          street: _streetController.text.trim(),
          buildingNumber: _buildingNumberController.text.trim(),
          floorNumber: _floorNumberController.text.trim(),
          apartmentNumber: _apartmentNumberController.text.trim(),
          landmark: _landmarkController.text.trim(),
          phone: _phoneController.text.trim(),
          isDefault: _isDefault,
        );
      }

      // Reload addresses
      await _loadAddresses();
      
      setState(() {
        _isEditing = false;
        _editingAddress = null;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(tr('address_saved'))),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: ${e.toString()}')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _deleteAddress(String id) async {
    setState(() {
      _isLoading = true;
    });

    try {
      await SupabaseService.deleteAddress(id);
      
      // Reload addresses
      await _loadAddresses();
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(tr('address_deleted'))),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: ${e.toString()}')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _setDefaultAddress(String id) async {
    setState(() {
      _isLoading = true;
    });

    try {
      await SupabaseService.setDefaultAddress(id);
      
      // Reload addresses
      await _loadAddresses();
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(tr('default_address_set'))),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: ${e.toString()}')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(tr('address_book')),
        centerTitle: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _isEditing
              ? _buildAddEditForm()
              : _buildAddressList(),
      floatingActionButton: !_isEditing
          ? FloatingActionButton(
              onPressed: () => _showAddEditAddressForm(),
              child: const Icon(Iconsax.add),
            )
          : null,
    );
  }

  Widget _buildAddressList() {
    if (_addresses.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Iconsax.location,
              size: 80.r,
              color: Colors.grey,
            ),
            SizedBox(height: 16.h),
            Text(
              tr('no_addresses'),
              style: TextStyle(
                fontSize: 18.sp,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 24.h),
            ElevatedButton.icon(
              onPressed: () => _showAddEditAddressForm(),
              icon: const Icon(Iconsax.add),
              label: Text(tr('add_new_address')),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(16.w),
      itemCount: _addresses.length,
      itemBuilder: (context, index) {
        final address = _addresses[index];
        return Card(
          margin: EdgeInsets.only(bottom: 16.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: address.isDefault
                ? BorderSide(
                    color: Theme.of(context).colorScheme.primary,
                    width: 2,
                  )
                : BorderSide.none,
          ),
          child: InkWell(
            onTap: widget.isSelecting
                ? () {
                    if (widget.onAddressSelected != null) {
                      widget.onAddressSelected!(address);
                      Navigator.pop(context);
                    }
                  }
                : null,
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          address.addressName,
                          style: TextStyle(
                            fontSize: 18.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      if (address.isDefault)
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 8.w,
                            vertical: 4.h,
                          ),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.primary,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            tr('default'),
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: Colors.white,
                            ),
                          ),
                        ),
                    ],
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    address.addressName,
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    address.emirate,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.grey[600],
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    '${address.area}, ${address.street}' + 
                    (address.buildingNumber != null && address.buildingNumber!.isNotEmpty ? ', ${tr("building")}: ${address.buildingNumber}' : '') +
                    (address.floorNumber != null && address.floorNumber!.isNotEmpty ? ', ${tr("floor")}: ${address.floorNumber}' : ''),
                    style: TextStyle(
                      fontSize: 14.sp,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 16.h),
                  if (!widget.isSelecting)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        if (!address.isDefault)
                          TextButton.icon(
                            onPressed: () => _setDefaultAddress(address.id.toString()),
                            icon: const Icon(Iconsax.tick_square),
                            label: Text(tr('set_as_default')),
                          ),
                        IconButton(
                          icon: const Icon(Iconsax.edit),
                          onPressed: () => _showAddEditAddressForm(address: address),
                        ),
                        IconButton(
                          icon: const Icon(Iconsax.trash, color: Colors.red),
                          onPressed: () {
                            showDialog(
                              context: context,
                              builder: (context) => AlertDialog(
                                title: Text(tr('delete_address')),
                                content: Text(tr('delete_address_confirm')),
                                actions: [
                                  TextButton(
                                    onPressed: () => Navigator.pop(context),
                                    child: Text(tr('cancel')),
                                  ),
                                  TextButton(
                                    onPressed: () {
                                      Navigator.pop(context);
                                      _deleteAddress(address.id.toString());
                                    },
                                    child: Text(
                                      tr('delete'),
                                      style: const TextStyle(color: Colors.red),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildAddEditForm() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(24.w),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              _editingAddress == null
                  ? tr('add_new_address')
                  : tr('edit_address'),
              style: TextStyle(
                fontSize: 24.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            SizedBox(height: 32.h),
            
            // Address Name Field
            TextFormField(
              controller: _nameController,
              decoration: InputDecoration(
                labelText: tr('address_name'),
                prefixIcon: const Icon(Iconsax.home),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return tr('address_name_required');
                }
                return null;
              },
            ),
            
            SizedBox(height: 16.h),
            
            // Emirate Dropdown
            DropdownButtonFormField<String>(
              value: _selectedEmirate,
              decoration: InputDecoration(
                labelText: tr('emirate'),
                prefixIcon: const Icon(Iconsax.location),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              items: _emirates.map((emirate) {
                return DropdownMenuItem<String>(
                  value: emirate,
                  child: Text(emirate),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedEmirate = value;
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return tr('emirate_required');
                }
                return null;
              },
            ),
            
            SizedBox(height: 16.h),
            
            // Area Field
            TextFormField(
              controller: _areaController,
              decoration: InputDecoration(
                labelText: tr('area'),
                prefixIcon: const Icon(Iconsax.map),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return tr('area_required');
                }
                return null;
              },
            ),
            
            SizedBox(height: 16.h),
            
            // Street Field
            TextFormField(
              controller: _streetController,
              decoration: InputDecoration(
                labelText: tr('street'),
                prefixIcon: const Icon(Iconsax.location_tick),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return tr('street_required');
                }
                return null;
              },
            ),
            
            SizedBox(height: 16.h),
            
            // Building Number Field
            TextFormField(
              controller: _buildingNumberController,
              decoration: InputDecoration(
                labelText: tr('building_number'),
                prefixIcon: const Icon(Iconsax.building),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
            
            SizedBox(height: 16.h),
            
            // Floor Number Field
            TextFormField(
              controller: _floorNumberController,
              decoration: InputDecoration(
                labelText: tr('floor_number'),
                prefixIcon: const Icon(Iconsax.arrow_up_1),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
            
            SizedBox(height: 16.h),
            
            // Apartment Number Field
            TextFormField(
              controller: _apartmentNumberController,
              decoration: InputDecoration(
                labelText: tr('apartment_number'),
                prefixIcon: const Icon(Iconsax.key),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
            
            SizedBox(height: 16.h),
            
            // Landmark Field
            TextFormField(
              controller: _landmarkController,
              decoration: InputDecoration(
                labelText: tr('landmark'),
                prefixIcon: const Icon(Iconsax.flag),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
            
            SizedBox(height: 16.h),
            
            // Phone Field
            TextFormField(
              controller: _phoneController,
              decoration: InputDecoration(
                labelText: tr('phone'),
                prefixIcon: const Icon(Iconsax.call),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              keyboardType: TextInputType.phone,
            ),
            
            SizedBox(height: 16.h),
            
            // Default Address Checkbox
            CheckboxListTile(
              value: _isDefault,
              onChanged: (value) {
                setState(() {
                  _isDefault = value ?? false;
                });
              },
              title: Text(tr('set_as_default')),
              controlAffinity: ListTileControlAffinity.leading,
              contentPadding: EdgeInsets.zero,
            ),
            
            SizedBox(height: 32.h),
            
            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _isEditing = false;
                        _editingAddress = null;
                      });
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey.shade200,
                      foregroundColor: Colors.black,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(tr('cancel')),
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _saveAddress,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(tr('save')),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

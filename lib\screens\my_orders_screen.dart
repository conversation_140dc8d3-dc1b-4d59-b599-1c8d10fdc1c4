import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:iconsax/iconsax.dart';
import 'package:soqia_joud/services/supabase_service.dart';
import 'package:flutter_animate/flutter_animate.dart';

class MyOrdersScreen extends StatefulWidget {
  const MyOrdersScreen({super.key});

  @override
  State<MyOrdersScreen> createState() => _MyOrdersScreenState();
}

class _MyOrdersScreenState extends State<MyOrdersScreen> {
  bool _isLoading = true;
  List<Map<String, dynamic>> _orders = [];
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadOrders();
  }

  Future<void> _loadOrders() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // استرجاع معرف المستخدم الحالي
      final userId = SupabaseService.getCurrentUserId();
      if (userId == null) {
        setState(() {
          _isLoading = false;
          _errorMessage = tr('user_not_logged_in');
        });
        return;
      }

      // استرجاع الطلبيات من قاعدة البيانات مع بيانات العناوين وتتبع الطلبيات
      final response = await SupabaseService.client
          .from('orders')
          .select('''
            *,
            addresses(*),
            order_tracking(*)
          ''')
          .eq('user_id', userId)
          .order('created_at', ascending: false);

      setState(() {
        _orders = List<Map<String, dynamic>>.from(response);
        _isLoading = false;
      });
      
      debugPrint('تم تحميل ${_orders.length} طلبية');
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = e.toString();
      });
      debugPrint('خطأ في تحميل الطلبيات: $e');
    }
  }

  String _getOrderStatusText(String status) {
    switch (status) {
      case 'pending':
        return tr('order_status_pending');
      case 'processing':
        return tr('order_status_processing');
      case 'shipping':
        return tr('order_status_shipping');
      case 'delivered':
        return tr('order_status_delivered');
      case 'cancelled':
        return tr('order_status_cancelled');
      default:
        return tr('order_status_unknown');
    }
  }

  Color _getOrderStatusColor(String status) {
    switch (status) {
      case 'pending':
        return Colors.orange;
      case 'processing':
        return Colors.blue;
      case 'shipping':
        return Colors.purple;
      case 'delivered':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(tr('my_orders')),
        centerTitle: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Iconsax.warning_2,
                        size: 64.sp,
                        color: Colors.red,
                      ),
                      SizedBox(height: 16.h),
                      Text(
                        _errorMessage!,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: Colors.red,
                        ),
                      ),
                      SizedBox(height: 24.h),
                      ElevatedButton(
                        onPressed: _loadOrders,
                        child: Text(tr('retry')),
                      ),
                    ],
                  ),
                )
              : _orders.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Iconsax.box,
                            size: 64.sp,
                            color: Colors.grey,
                          ),
                          SizedBox(height: 16.h),
                          Text(
                            tr('no_orders_found'),
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: Colors.grey,
                            ),
                          ),
                          SizedBox(height: 24.h),
                          ElevatedButton(
                            onPressed: () {
                              Navigator.pushNamed(context, '/new-order');
                            },
                            child: Text(tr('create_new_order')),
                          ),
                        ],
                      ),
                    )
                  : RefreshIndicator(
                      onRefresh: _loadOrders,
                      child: ListView.builder(
                        padding: EdgeInsets.all(16.w),
                        itemCount: _orders.length,
                        itemBuilder: (context, index) {
                          final order = _orders[index];
                          // استخدام حالة الطلبية من جدول الطلبيات مباشرة بدلاً من جدول تتبع الطلبيات
                          final status = order['status'] ?? 'pending';
                          
                          return Card(
                            margin: EdgeInsets.only(bottom: 16.h),
                            elevation: 2,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12.r),
                            ),
                            child: InkWell(
                              borderRadius: BorderRadius.circular(12.r),
                              onTap: () {
                                Navigator.pushNamed(
                                  context,
                                  '/order-tracking',
                                  arguments: {'orderId': order['id'].toString()},
                                );
                              },
                              child: Padding(
                                padding: EdgeInsets.all(16.w),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // رأس البطاقة مع رقم الطلبية والحالة
                                    Container(
                                      padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 12.w),
                                      decoration: BoxDecoration(
                                        color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(12.r),
                                      ),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          Expanded(
                                            child: Text(
                                              "${tr('order_number')}: ${order['order_number'] ?? ''}",
                                              style: TextStyle(
                                                fontSize: 16.sp,
                                                fontWeight: FontWeight.bold,
                                                color: Theme.of(context).colorScheme.primary,
                                              ),
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                          Container(
                                            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                                            decoration: BoxDecoration(
                                              color: _getOrderStatusColor(status),
                                              borderRadius: BorderRadius.circular(8.r),
                                            ),
                                            child: Text(
                                              _getOrderStatusText(status),
                                              style: TextStyle(
                                                fontSize: 12.sp,
                                                fontWeight: FontWeight.bold,
                                                color: Colors.white,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(height: 16.h),
                                    
                                    // معلومات الطلبية
                                    Container(
                                      padding: EdgeInsets.all(12.w),
                                      decoration: BoxDecoration(
                                        color: Colors.grey.withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(12.r),
                                      ),
                                      child: Column(
                                        children: [
                                          _buildInfoRow(
                                            tr('quantity'),
                                            "${order['quantity'] ?? 0} ${tr('bottles')}",
                                            Iconsax.box,
                                          ),
                                          Divider(height: 16.h, thickness: 0.5),
                                          _buildInfoRow(
                                            tr('total_amount'),
                                            "${order['total_price'] ?? 0} ${tr('currency')}",
                                            Iconsax.money,
                                          ),
                                          Divider(height: 16.h, thickness: 0.5),
                                          _buildInfoRow(
                                            tr('date'),
                                            DateFormat('yyyy-MM-dd').format(
                                              DateTime.parse(order['order_date'] ?? order['created_at']),
                                            ),
                                            Iconsax.calendar,
                                          ),
                                        ],
                                      ),
                                    ),
                                    
                                    SizedBox(height: 16.h),
                                    
                                    // زر تتبع الطلبية
                                    SizedBox(
                                      width: double.infinity,
                                      child: ElevatedButton.icon(
                                        onPressed: () {
                                          Navigator.pushNamed(
                                            context,
                                            '/order-tracking',
                                            arguments: {'orderId': order['id'].toString()},
                                          );
                                        },
                                        icon: const Icon(Iconsax.truck),
                                        label: Text(tr('track_order')),
                                        style: ElevatedButton.styleFrom(
                                          padding: EdgeInsets.symmetric(vertical: 12.h),
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(10.r),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ).animate()
                            .fadeIn(
                              duration: const Duration(milliseconds: 300),
                              delay: Duration(milliseconds: index * 100),
                            )
                            .slideX(
                              begin: 0.1,
                              end: 0,
                              curve: Curves.easeOutQuad,
                              duration: const Duration(milliseconds: 300),
                              delay: Duration(milliseconds: index * 100),
                            );
                        },
                      ),
                    ),
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon, {Color? textColor}) {
    return Row(
      children: [
        Icon(
          icon,
          size: 18.sp,
          color: Theme.of(context).colorScheme.primary,
        ),
        SizedBox(width: 8.w),
        Text(
          '$label: ',
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14.sp,
              color: textColor,
              fontWeight: textColor != null ? FontWeight.w600 : null,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}

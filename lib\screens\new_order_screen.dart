import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:iconsax/iconsax.dart';
import 'package:soqia_joud/models/address.dart';
import 'package:soqia_joud/screens/address_book_screen.dart';
import 'package:soqia_joud/screens/login_screen.dart';
import 'package:soqia_joud/screens/payment_gateway_screen.dart';
import 'package:soqia_joud/services/supabase_service.dart';

class NewOrderScreen extends StatefulWidget {
  const NewOrderScreen({super.key});

  @override
  State<NewOrderScreen> createState() => _NewOrderScreenState();
}

class _NewOrderScreenState extends State<NewOrderScreen> {
  int quantity = 1; // العودة إلى استخدام quantity
  final double unitPrice = 5.0; // سعر الوحدة ثابت 5 دراهم
  double get totalPrice => quantity * unitPrice; // العودة إلى استخدام quantity
  
  List<Address> _addresses = [];
  Address? _selectedAddress;
  bool _isLoading = true;
  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    _checkAuthAndLoadAddresses();
  }
  
  Future<void> _checkAuthAndLoadAddresses() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      final currentUser = SupabaseService.getCurrentUser();
      if (currentUser == null) {
        // Not logged in, redirect to login screen
        if (mounted) {
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(builder: (context) => const LoginScreen()),
          );
        }
        return;
      }
      
      await _loadAddresses();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: ${e.toString()}')),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
  
  Future<void> _loadAddresses() async {
    try {
      final addressesData = await SupabaseService.getUserAddresses();
      
      setState(() {
        _addresses = addressesData
            .map((item) => Address.fromJson(item))
            .toList();
            
        // Set default address as selected if available
        final defaultAddress = _addresses.firstWhere(
          (address) => address.isDefault,
          orElse: () => _addresses.isNotEmpty ? _addresses.first : Address(
            id: '',
            addressName: '',
            emirate: '',
            area: '',
            street: '',
          ),
        );
        
        if (_addresses.isNotEmpty) {
          _selectedAddress = defaultAddress;
        }
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: ${e.toString()}')),
      );
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(tr('new_order')),
        centerTitle: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // قسم اختيار الكمية
            _buildSectionTitle(tr('quantity')),
            SizedBox(height: 16.h),
            _buildQuantitySelector(),
            SizedBox(height: 24.h),
            
            // قسم السعر
            _buildSectionTitle(tr('unit_price')),
            SizedBox(height: 8.h),
            Text(
              '$unitPrice AED',
              style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 16.h),
            
            _buildSectionTitle(tr('total_price')),
            SizedBox(height: 8.h),
            Text(
              '$totalPrice AED',
              style: TextStyle(
                fontSize: 24.sp, 
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            SizedBox(height: 24.h),
            
            // قسم العنوان
            _buildSectionTitle(tr('address')),
            SizedBox(height: 16.h),
            _buildAddressSelector(),
            SizedBox(height: 32.h),
            
            // زر تأكيد الطلب
            SizedBox(
              width: double.infinity,
              height: 56.h,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                onPressed: _isSubmitting || _addresses.isEmpty || _selectedAddress == null
                  ? null
                  : _confirmOrder,
                child: _isSubmitting
                  ? SizedBox(
                      height: 24.h,
                      width: 24.w,
                      child: const CircularProgressIndicator(color: Colors.white),
                    )
                  : Text(
                      tr('pay_now'),
                      style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.bold),
                    ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 18.sp,
        fontWeight: FontWeight.bold,
        color: Theme.of(context).colorScheme.secondary,
      ),
    );
  }

  Widget _buildQuantitySelector() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            onPressed: () {
              if (quantity > 1) {
                setState(() {
                  quantity--;
                });
              }
            },
            icon: Icon(
              Icons.remove_circle_outline,
              color: quantity > 1
                  ? Theme.of(context).colorScheme.primary
                  : Colors.grey,
              size: 32.w,
            ),
          ),
          Text(
            quantity.toString(),
            style: TextStyle(fontSize: 24.sp, fontWeight: FontWeight.bold),
          ),
          IconButton(
            onPressed: () {
              setState(() {
                quantity++;
              });
            },
            icon: Icon(
              Icons.add_circle_outline,
              color: Theme.of(context).colorScheme.primary,
              size: 32.w,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddressSelector() {
    if (_addresses.isEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            tr('no_addresses'),
            style: TextStyle(fontSize: 16.sp),
          ),
          SizedBox(height: 16.h),
          InkWell(
            onTap: _addNewAddress,
            child: Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Theme.of(context).colorScheme.primary),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Iconsax.add_circle,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    tr('add_new_address'),
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.bold,
                      fontSize: 16.sp,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      );
    } else {
      return Column(
        children: [
          ..._addresses.map((address) => _buildAddressItem(address)).toList(),
          SizedBox(height: 16.h),
          TextButton.icon(
            onPressed: _addNewAddress,
            icon: const Icon(Iconsax.add_circle),
            label: Text(tr('add_new_address')),
          ),
        ],
      );
    }
  }

  Widget _buildAddressItem(Address address) {
    final isSelected = _selectedAddress?.id == address.id;
    
    return Card(
      margin: EdgeInsets.only(bottom: 8.h),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: isSelected
              ? Theme.of(context).colorScheme.primary
              : Colors.transparent,
          width: 2,
        ),
      ),
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedAddress = address;
          });
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            address.addressName,
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        if (address.isDefault)
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 8.w,
                              vertical: 4.h,
                            ),
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.primary,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              tr('default'),
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: Colors.white,
                              ),
                            ),
                          ),
                      ],
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      address.emirate,
                      style: TextStyle(fontSize: 14.sp),
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      '${address.area}, ${address.street}' + 
                      (address.buildingNumber != null && address.buildingNumber!.isNotEmpty ? ', ${tr("building")}: ${address.buildingNumber}' : '') +
                      (address.floorNumber != null && address.floorNumber!.isNotEmpty ? ', ${tr("floor")}: ${address.floorNumber}' : ''),
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.grey.shade700,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              Radio<Address>(
                value: address,
                groupValue: _selectedAddress,
                onChanged: (Address? value) {
                  setState(() {
                    _selectedAddress = value;
                  });
                },
                activeColor: Theme.of(context).colorScheme.primary,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _addNewAddress() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddressBookScreen(
          isSelecting: true,
          onAddressSelected: (address) {
            setState(() {
              _selectedAddress = address;
            });
          },
        ),
      ),
    );
    
    if (result == true) {
      // Refresh addresses list
      _loadAddresses();
    }
  }
  
  Future<void> _confirmOrder() async {
    if (_selectedAddress == null) return;
    
    setState(() {
      _isSubmitting = true;
    });
    
    String? orderId;
    
    try {
      // إرسال بيانات الطلبية إلى قاعدة البيانات
      final createdOrderId = await SupabaseService.createOrder(
        quantity: quantity,
        unitPrice: unitPrice,
        totalPrice: totalPrice,
        addressId: _selectedAddress!.id,
        status: 'pending',
      );
      
      orderId = createdOrderId;
      
      // عرض رسالة نجاح
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(tr('order_created_successfully'))),
        );
      }
    } catch (e) {
      print('Error creating order: $e');
      // عرض رسالة خطأ
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ في إنشاء الطلبية، لكن سنستمر في عملية الدفع')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
        
        // انتقل إلى صفحة بوابة الدفع حتى في حالة حدوث خطأ، طالما تم إنشاء الطلبية بنجاح
        if (orderId != null) {
          final String nonNullOrderId = orderId; // تحويل صريح من String? إلى String
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => PaymentGatewayScreen(
                amount: totalPrice,
                orderId: nonNullOrderId,
              ),
            ),
          );
        }
      }
    }
  }
}

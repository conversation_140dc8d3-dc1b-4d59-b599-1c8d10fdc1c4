import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:iconsax/iconsax.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:soqia_joud/services/supabase_service.dart';
import 'dart:math' as math;

class OrderTrackingScreen extends StatefulWidget {
  final String? orderId;

  const OrderTrackingScreen({super.key, this.orderId});

  @override
  State<OrderTrackingScreen> createState() => _OrderTrackingScreenState();
}

class _OrderTrackingScreenState extends State<OrderTrackingScreen> {
  // حالة تحميل البيانات
  bool _isLoading = true;
  String? _errorMessage;

  // بيانات الطلبية
  Map<String, dynamic>? _orderData;
  Map<String, dynamic>? _trackingData;
  Map<String, dynamic>? _addressData;

  // مراحل توصيل الطلبية
  final List<String> _stages = [
    'استلام الطلبية',
    'جاري المعالجة',
    'جاري الشحن',
    'جاري التوصيل',
    'تم التوزيع',
  ];

  // الأيقونات لكل مرحلة
  final List<IconData> _icons = [
    Iconsax.receipt,
    Iconsax.document_text,
    Iconsax.box,
    Iconsax.truck,
    Iconsax.tick_circle,
  ];

  // مراحل الطلبية الملغية
  final List<String> _cancelledStages = ['استلام الطلبية', 'تم الإلغاء'];

  // الأيقونات للطلبية الملغية
  final List<IconData> _cancelledIcons = [
    Iconsax.receipt,
    Iconsax.close_circle,
  ];

  // المرحلة الحالية (سيتم تحديثها من قاعدة البيانات)
  int _currentStage = 0;

  String? _effectiveOrderId;

  @override
  void initState() {
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (_effectiveOrderId == null) {
      // الأولوية للـ constructor
      if (widget.orderId != null) {
        _effectiveOrderId = widget.orderId;
      } else {
        // محاولة الحصول على المعرف من الـ route arguments
        final args = ModalRoute.of(context)?.settings.arguments;
        if (args is Map && args['orderId'] != null) {
          _effectiveOrderId = args['orderId'].toString();
        }
      }

      // طباعة معرف الطلبية للتشخيص
      debugPrint('معرف الطلبية المستلم: $_effectiveOrderId');

      // إذا تم تعيين معرف الطلبية، قم بتحميل البيانات
      if (_effectiveOrderId != null) {
        _loadOrderData();
      } else {
        setState(() {
          _isLoading = false;
          _errorMessage = 'لم يتم تحديد رقم الطلبية';
        });
      }
    }
  }

  Future<void> _loadOrderData() async {
    if (_effectiveOrderId == null) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'لم يتم تحديد رقم الطلبية';
      });
      return;
    }

    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // طباعة معرف الطلبية للتشخيص
      debugPrint('جاري تحميل الطلبية بالمعرف: $_effectiveOrderId');

      // استرجاع بيانات الطلبية من قاعدة البيانات
      final orderData = await SupabaseService.getOrderDetails(
        _effectiveOrderId!,
      );

      // طباعة البيانات المستلمة للتشخيص
      debugPrint('تم استلام بيانات الطلبية: ${orderData.toString()}');

      // استرجاع بيانات تتبع الطلبية باستخدام معرف الطلبية من البيانات المسترجعة
      Map<String, dynamic>? trackingResponse;
      try {
        trackingResponse = await SupabaseService.client
            .from('order_tracking')
            .select('*')
            .eq('order_id', orderData['id'])
            .single();
      } catch (e) {
        debugPrint('Error fetching tracking data: $e');
        // إذا فشل استرجاع بيانات التتبع، استمر مع بيانات الطلبية فقط
      }

      // تحديد المرحلة الحالية بناءً على حالة الطلبية
      int currentStage = 0;
      final status = orderData['status'] ?? 'pending';
      switch (status) {
        case 'pending':
          currentStage = 0; // استلام الطلبية
          break;
        case 'processing':
          currentStage = 1; // جاري المعالجة
          break;
        case 'shipping':
          currentStage = 2; // جاري الشحن
          break;
        case 'out_for_delivery':
          currentStage = 3; // جاري التوصيل
          break;
        case 'delivered':
          currentStage = 4; // تم التوزيع
          break;
        case 'cancelled':
          currentStage = 1; // تم الإلغاء (في الخط الزمني للطلبيات الملغية)
          break;
        default:
          currentStage = 0;
      }

      // استرجاع بيانات العنوان
      Map<String, dynamic>? addressData;
      if (orderData.containsKey('addresses') &&
          orderData['addresses'] != null) {
        // إذا كان العنوان موجودًا كقائمة
        if (orderData['addresses'] is List &&
            (orderData['addresses'] as List).isNotEmpty) {
          addressData = (orderData['addresses'] as List).first;
        }
        // إذا كان العنوان موجودًا كخريطة
        else if (orderData['addresses'] is Map) {
          addressData = orderData['addresses'];
        }
      }

      // استرجاع بيانات المبلغ
      double? totalAmount;
      if (orderData.containsKey('total_price') &&
          orderData['total_price'] != null) {
        try {
          if (orderData['total_price'] is num) {
            totalAmount = (orderData['total_price'] as num).toDouble();
          } else if (orderData['total_price'] is String) {
            totalAmount = double.tryParse(orderData['total_price']);
          }
        } catch (e) {
          debugPrint('خطأ في تحويل المبلغ: $e');
        }
      }

      // تحديث البيانات في الحالة
      setState(() {
        _orderData = orderData;
        _trackingData = trackingResponse;
        _addressData = addressData;
        if (totalAmount != null) {
          _orderData!['total_price'] = totalAmount;
        }
        _currentStage = currentStage;
        _isLoading = false;
        _errorMessage = null; // تأكد من إزالة أي رسائل خطأ سابقة
      });

      // طباعة بيانات العنوان والمبلغ للتشخيص
      debugPrint('بيانات العنوان: ${addressData.toString()}');
      debugPrint('المبلغ الإجمالي: $totalAmount');
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = e.toString();
      });
      debugPrint('خطأ في تحميل بيانات الطلبية: $e');
    }
  }

  // تحديد لون حالة الطلبية والنص المناسب
  Color _getStatusColor() {
    if (_orderData == null) return Colors.grey;

    final status = _orderData!['status'] ?? 'pending';
    switch (status) {
      case 'pending':
        return Colors.orange;
      case 'processing':
        return Colors.blue;
      case 'shipping':
        return Colors.indigo;
      case 'out_for_delivery':
        return Colors.deepPurple;
      case 'delivered':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText() {
    if (_orderData == null) return tr('pending');

    final status = _orderData!['status'] ?? 'pending';
    switch (status) {
      case 'pending':
        return tr('pending');
      case 'processing':
        return tr('processing');
      case 'shipping':
        return tr('shipping');
      case 'out_for_delivery':
        return tr('out_for_delivery');
      case 'delivered':
        return tr('delivered');
      case 'cancelled':
        return tr('cancelled');
      default:
        return tr('pending');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(tr('track_order')),
        centerTitle: true,
        bottom: _orderData != null
            ? PreferredSize(
                preferredSize: Size.fromHeight(60.h),
                child: Container(
                  width: double.infinity,
                  padding: EdgeInsets.symmetric(vertical: 8.h),
                  child: Center(
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 20.w,
                        vertical: 10.h,
                      ),
                      decoration: BoxDecoration(
                        color: _getStatusColor().withOpacity(0.1),
                        borderRadius: BorderRadius.circular(20.r),
                        border: Border.all(color: _getStatusColor()),
                        boxShadow: [
                          BoxShadow(
                            color: _getStatusColor().withOpacity(0.2),
                            blurRadius: 4,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Text(
                        _getStatusText(),
                        style: TextStyle(
                          color: _getStatusColor(),
                          fontWeight: FontWeight.bold,
                          fontSize: 16.sp,
                        ),
                      ),
                    ),
                  ),
                ),
              )
            : null,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Iconsax.warning_2, size: 64.sp, color: Colors.red),
                  SizedBox(height: 16.h),
                  Text(
                    _errorMessage!,
                    textAlign: TextAlign.center,
                    style: TextStyle(fontSize: 16.sp, color: Colors.red),
                  ),
                  SizedBox(height: 24.h),
                  ElevatedButton(
                    onPressed: _loadOrderData,
                    child: Text(tr('retry')),
                  ),
                ],
              ),
            )
          : RefreshIndicator(
              onRefresh: _loadOrderData,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Column(
                  children: [
                    // معلومات الطلبية
                    _buildOrderInfo(),

                    // شريط تتبع المراحل
                    _buildTrackingTimeline(),

                    // تفاصيل إضافية
                    _buildAdditionalDetails(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildOrderInfo() {
    if (_orderData == null) return const SizedBox.shrink();

    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رقم الطلبية
          Row(
            children: [
              Icon(
                Iconsax.document_code,
                size: 20.sp,
                color: Theme.of(context).colorScheme.primary,
              ),
              SizedBox(width: 8.w),
              Text(
                '${tr('order_number')}: #${_orderData!['id'].toString().substring(0, math.min(8, _orderData!['id'].toString().length))}',
                style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.bold),
              ),
            ],
          ),

          Divider(height: 24.h),

          // عنوان التوصيل
          if (_addressData != null) ...[
            Text(
              tr('delivery_address'),
              style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8.h),
            Text(
              '${_addressData!['address'] ?? ''}, ${_addressData!['city'] ?? ''}, ${_addressData!['country'] ?? ''}',
              style: TextStyle(fontSize: 14.sp),
            ),
            Divider(height: 24.h),
          ],

          // تاريخ الطلبية
          if (_orderData!['created_at'] != null) ...[
            Row(
              children: [
                Icon(
                  Iconsax.calendar,
                  size: 20.sp,
                  color: Theme.of(context).colorScheme.primary,
                ),
                SizedBox(width: 8.w),
                Text(
                  '${tr('order_date')}: ${DateFormat('yyyy-MM-dd').format(DateTime.parse(_orderData!['created_at']))}',
                  style: TextStyle(fontSize: 14.sp),
                ),
              ],
            ),
            SizedBox(height: 12.h),
          ],

          // إجمالي الطلبية
          Row(
            children: [
              Icon(
                Iconsax.money,
                size: 20.sp,
                color: Theme.of(context).colorScheme.primary,
              ),
              SizedBox(width: 8.w),
              Text(
                '${tr('total')}: ${_orderData!['total_price']} ${tr('sar')}',
                style: TextStyle(fontSize: 14.sp),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTrackingTimeline() {
    // تحديد ما إذا كانت الطلبية ملغية
    final bool isCancelled =
        _orderData != null && _orderData!['status'] == 'cancelled';

    // اختيار المراحل والأيقونات المناسبة
    final List<String> currentStages = isCancelled ? _cancelledStages : _stages;
    final List<IconData> currentIcons = isCancelled ? _cancelledIcons : _icons;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان الخط الزمني
          Row(
            children: [
              Icon(
                isCancelled ? Iconsax.close_circle : Iconsax.timer,
                color: isCancelled
                    ? Colors.red
                    : Theme.of(context).primaryColor,
                size: 20.sp,
              ),
              SizedBox(width: 8.w),
              Text(
                isCancelled ? 'حالة الطلبية الملغية' : tr('order_tracking'),
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: isCancelled
                      ? Colors.red
                      : Theme.of(context).primaryColor,
                ),
              ),
            ],
          ),
          SizedBox(height: 24.h),

          // مراحل التتبع
          ListView.separated(
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            itemCount: currentStages.length,
            separatorBuilder: (context, index) =>
                _buildSeparator(index, isCancelled),
            itemBuilder: (context, index) =>
                _buildTrackingStep(index, isCancelled),
          ),
        ],
      ),
    );
  }

  Widget _buildSeparator(int index, [bool isCancelled = false]) {
    // تحديد عدد المراحل بناءً على نوع الطلبية
    final int stagesLength = isCancelled
        ? _cancelledStages.length
        : _stages.length;

    // لا نضيف خط فاصل بعد آخر مرحلة
    if (index == stagesLength - 1) return const SizedBox.shrink();

    // تحديد لون الخط الفاصل بناءً على حالة المرحلة
    bool isCurrentStageCompleted = false;
    if (_trackingData != null) {
      switch (index) {
        case 0: // استلام الطلبية
          isCurrentStageCompleted = _trackingData!['received_at'] != null;
          break;
        case 1: // جاري المعالجة
          isCurrentStageCompleted = _trackingData!['processing_at'] != null;
          break;
        case 2: // جاري الشحن
          isCurrentStageCompleted = _trackingData!['shipping_at'] != null;
          break;
        case 3: // جاري التوصيل
          isCurrentStageCompleted =
              _trackingData!['out_for_delivery_at'] != null;
          break;
        case 4: // تم التوزيع
          isCurrentStageCompleted = _trackingData!['delivered_at'] != null;
          break;
      }
    }

    return Container(
      margin: EdgeInsets.only(left: 20.w),
      height: 30.h,
      width: 2.w,
      color: isCurrentStageCompleted ? Colors.green : Colors.grey.shade300,
    );
  }

  Widget _buildTrackingStep(int index, [bool isCancelled = false]) {
    // اختيار المراحل والأيقونات المناسبة
    final List<String> currentStages = isCancelled ? _cancelledStages : _stages;
    final List<IconData> currentIcons = isCancelled ? _cancelledIcons : _icons;

    // تحديد ما إذا كانت المرحلة محدثة في قاعدة البيانات
    bool isUpdatedInDB = false;
    String? dateString;

    if (_trackingData != null && !isCancelled) {
      switch (index) {
        case 0: // استلام الطلبية
          dateString = _trackingData!['received_at'];
          isUpdatedInDB = dateString != null;
          break;
        case 1: // جاري المعالجة
          dateString = _trackingData!['processing_at'];
          isUpdatedInDB = dateString != null;
          break;
        case 2: // جاري الشحن
          dateString = _trackingData!['shipping_at'];
          isUpdatedInDB = dateString != null;
          break;
        case 3: // جاري التوصيل
          dateString = _trackingData!['out_for_delivery_at'];
          isUpdatedInDB = dateString != null;
          break;
        case 4: // تم التوزيع
          dateString = _trackingData!['delivered_at'];
          isUpdatedInDB = dateString != null;
          break;
      }
    } else if (isCancelled && _trackingData != null) {
      // للطلبيات الملغية
      switch (index) {
        case 0: // استلام الطلبية
          dateString = _trackingData!['received_at'];
          isUpdatedInDB = dateString != null;
          break;
        case 1: // تم الإلغاء
          dateString = _trackingData!['cancelled_at'];
          isUpdatedInDB = dateString != null;
          break;
      }
    }

    final bool isCurrent = index == _currentStage;

    // تحديد الألوان بناءً على حالة المرحلة
    Color circleColor;
    Color textColor;

    if (isCancelled && index == 1) {
      // مرحلة الإلغاء باللون الأحمر
      circleColor = Colors.red;
      textColor = Colors.red;
    } else if (isUpdatedInDB) {
      // المراحل المحدثة في قاعدة البيانات باللون الأخضر
      circleColor = Colors.green;
      textColor = Colors.green;
    } else if (isCurrent) {
      // المرحلة الحالية باللون الأخضر المضيء
      circleColor = Colors.green.shade500;
      textColor = Colors.green.shade500;
    } else {
      // المراحل الغير محدثة بلون أكثر وضوحاً
      circleColor = Colors.grey.shade400;
      textColor = Colors.grey.shade700;
    }

    // تحديد ما إذا كانت المرحلة نشطة (المرحلة الحالية أو محدثة في قاعدة البيانات)
    final bool isActive = isUpdatedInDB || isCurrent;

    // تنسيق التاريخ إذا كان موجوداً
    String formattedDate = '';
    if (dateString != null) {
      try {
        final DateTime date = DateTime.parse(dateString);
        formattedDate =
            '${date.hour}:${date.minute.toString().padLeft(2, '0')} ${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
      } catch (e) {
        formattedDate = dateString;
      }
    }

    // إنشاء عنصر واجهة المرحلة مع تأثيرات الرسوم المتحركة
    Widget stepWidget = Container(
      margin: EdgeInsets.symmetric(vertical: 4.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // الأيقونة
          Container(
            width: 40.w,
            height: 40.w,
            decoration: BoxDecoration(
              color: circleColor,
              shape: BoxShape.circle,
              // إضافة توهج خفيف للمرحلة الحالية
              boxShadow: isCurrent
                  ? [
                      BoxShadow(
                        color: Colors.green.withOpacity(0.3),
                        blurRadius: 8,
                        spreadRadius: 2,
                      ),
                    ]
                  : [],
            ),
            child: Icon(
              isCancelled ? _cancelledIcons[index] : _icons[index],
              color: Colors.white,
              size: 20.sp,
            ),
          ),

          SizedBox(width: 12.w),

          // النص والتاريخ
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isCancelled ? _cancelledStages[index] : _stages[index],
                  style: TextStyle(
                    fontSize: 15.sp,
                    fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                    color: textColor,
                  ),
                ),

                if (isUpdatedInDB && formattedDate.isNotEmpty) ...[
                  SizedBox(height: 4.h),
                  Text(
                    formattedDate,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );

    // إضافة تأثيرات الرسوم المتحركة
    return AnimatedOpacity(
      opacity: isActive ? 1.0 : 0.7,
      duration: const Duration(milliseconds: 300),
      child: AnimatedSlide(
        offset: Offset(0, 0),
        duration: Duration(milliseconds: 300 + (index * 100)),
        curve: Curves.easeOutQuad,
        child: stepWidget,
      ),
    );
  }

  // دالة للحصول على نص العنوان بشكل آمن
  String _getAddressText() {
    try {
      // إذا لم تكن هناك بيانات عنوان على الإطلاق
      if (_addressData == null) {
        return tr('no_address');
      }

      // إذا كان العنوان عبارة عن قائمة (مصفوفة)
      if (_addressData is List) {
        if ((_addressData as List).isEmpty) {
          return tr('no_address');
        }

        var firstAddress = (_addressData as List).first;
        if (firstAddress == null) {
          return tr('no_address');
        }

        // التحقق من وجود مفتاح العنوان الكامل
        if (firstAddress is Map &&
            firstAddress.containsKey('full_address') &&
            firstAddress['full_address'] != null) {
          return firstAddress['full_address'].toString();
        }

        // محاولة بناء العنوان من التفاصيل المتاحة
        if (firstAddress is Map) {
          String address = '';

          if (firstAddress.containsKey('area') &&
              firstAddress['area'] != null) {
            address += firstAddress['area'].toString();
          }

          if (firstAddress.containsKey('street') &&
              firstAddress['street'] != null) {
            if (address.isNotEmpty) address += ', ';
            address += firstAddress['street'].toString();
          }

          if (firstAddress.containsKey('building') &&
              firstAddress['building'] != null) {
            if (address.isNotEmpty) address += ', ';
            address +=
                tr('building') + ': ' + firstAddress['building'].toString();
          }

          return address.isNotEmpty ? address : tr('no_address');
        }
      }
      // إذا كان العنوان عبارة عن خريطة (Map)
      else if (_addressData is Map) {
        Map addressMap = _addressData as Map;

        // التحقق من وجود مفتاح العنوان الكامل
        if (addressMap.containsKey('full_address') &&
            addressMap['full_address'] != null) {
          return addressMap['full_address'].toString();
        }

        // محاولة بناء العنوان من التفاصيل المتاحة
        String address = '';

        if (addressMap.containsKey('area') && addressMap['area'] != null) {
          address += addressMap['area'].toString();
        }

        if (addressMap.containsKey('street') && addressMap['street'] != null) {
          if (address.isNotEmpty) address += ', ';
          address += addressMap['street'].toString();
        }

        if (addressMap.containsKey('building') &&
            addressMap['building'] != null) {
          if (address.isNotEmpty) address += ', ';
          address += tr('building') + ': ' + addressMap['building'].toString();
        }

        // محاولة أخرى للحصول على العنوان من حقول مختلفة
        if (address.isEmpty) {
          if (addressMap.containsKey('address') &&
              addressMap['address'] != null) {
            address = addressMap['address'].toString();
          } else if (addressMap.containsKey('address_line1') &&
              addressMap['address_line1'] != null) {
            address = addressMap['address_line1'].toString();

            if (addressMap.containsKey('address_line2') &&
                addressMap['address_line2'] != null &&
                addressMap['address_line2'].toString().isNotEmpty) {
              address += ', ' + addressMap['address_line2'].toString();
            }
          }
        }

        return address.isNotEmpty ? address : tr('no_address');
      }

      // إذا وصلنا إلى هنا، فهذا يعني أننا لم نتمكن من استخراج العنوان
      debugPrint('لم نتمكن من استخراج العنوان من البيانات: $_addressData');
      return tr('no_address');
    } catch (e) {
      debugPrint('خطأ في الحصول على العنوان: $e');
      return tr('no_address');
    }
  }

  // هذه المساحة محجوزة للدوال المساعدة الإضافية إذا لزم الأمر

  Widget _buildAdditionalDetails() {
    // إضافة طباعة تشخيصية لمعرفة البيانات المتاحة
    debugPrint('بيانات الطلبية: ${_orderData.toString()}');
    debugPrint('بيانات العنوان: ${_addressData.toString()}');

    // إذا لم تكن هناك بيانات طلبية، لا تعرض شيئاً
    if (_orderData == null) {
      return const SizedBox.shrink();
    }

    // عرض تفاصيل الطلبية (العنوان والمبلغ والملاحظات)
    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان التوصيل
          if (_addressData != null) ...[
            Row(
              children: [
                Icon(Iconsax.location, size: 18.sp, color: Colors.green),
                SizedBox(width: 8.w),
                Text(
                  tr('delivery_address'),
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8.h),
            Container(
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Text(
                // استخدام دالة آمنة للحصول على نص العنوان
                _getAddressText(),
                style: TextStyle(fontSize: 14.sp),
              ),
            ),
            SizedBox(height: 16.h),
          ],

          // المبلغ الإجمالي
          if (_orderData != null && _orderData!.containsKey('total_price')) ...[
            Row(
              children: [
                Icon(Iconsax.money, size: 18.sp, color: Colors.green),
                SizedBox(width: 8.w),
                Text(
                  tr('total_amount'),
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8.h),
            Container(
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Text(
                "${_orderData!['total_price'] ?? '0.0'} ${tr('currency')}",
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                  color: Colors.green.shade700,
                ),
              ),
            ),
            SizedBox(height: 16.h),
          ],

          // الملاحظات إذا كانت موجودة
          if (_orderData != null &&
              _orderData!.containsKey('notes') &&
              _orderData!['notes'] != null &&
              _orderData!['notes'].toString().isNotEmpty) ...[
            Row(
              children: [
                Icon(Iconsax.note_text, size: 18.sp, color: Colors.green),
                SizedBox(width: 8.w),
                Text(
                  tr('notes'),
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8.h),
            Container(
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Text(
                _orderData!['notes'].toString(),
                style: TextStyle(fontSize: 14.sp),
              ),
            ),
          ],
        ],
      ),
    );
  }
}

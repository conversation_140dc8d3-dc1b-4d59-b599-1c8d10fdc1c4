import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:iconsax/iconsax.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:soqia_joud/services/supabase_service.dart';

class OrderTrackingScreen extends StatefulWidget {
  final String? orderId;

  const OrderTrackingScreen({super.key, this.orderId});

  @override
  State<OrderTrackingScreen> createState() => _OrderTrackingScreenState();
}

class _OrderTrackingScreenState extends State<OrderTrackingScreen> {
  // حالة تحميل البيانات
  bool _isLoading = true;
  String? _errorMessage;

  // بيانات الطلبية
  Map<String, dynamic>? _orderData;
  Map<String, dynamic>? _trackingData;
  Map<String, dynamic>? _addressData;

  // مراحل توصيل الطلبية
  final List<String> _stages = [
    'استلام الطلبية',
    'جاري المعالجة',
    'جاري الشحن',
    'جاري التوصيل',
    'تم التوزيع',
  ];

  // الأيقونات لكل مرحلة
  final List<IconData> _icons = [
    Iconsax.receipt,
    Iconsax.document_text,
    Iconsax.box,
    Iconsax.truck,
    Iconsax.tick_circle,
  ];

  // المرحلة الحالية (سيتم تحديثها من قاعدة البيانات)
  int _currentStage = 0;

  @override
  void initState() {
    super.initState();
    _loadOrderData();
  }

  Future<void> _loadOrderData() async {
    if (widget.orderId == null) {
      setState(() {
        _isLoading = false;
        _errorMessage = tr('order_id_not_available');
      });
      return;
    }

    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // طباعة معرف الطلبية للتشخيص
      print('Loading order with ID: ${widget.orderId}');

      // استرجاع بيانات الطلبية من قاعدة البيانات
      final orderData = await SupabaseService.getOrderDetails(widget.orderId!);

      // استرجاع بيانات تتبع الطلبية باستخدام معرف الطلبية من البيانات المسترجعة
      Map<String, dynamic>? trackingResponse;
      try {
        trackingResponse = await SupabaseService.client
            .from('order_tracking')
            .select('*')
            .eq('order_id', orderData['id'])
            .single();
      } catch (e) {
        print('Error fetching tracking data: $e');
        // إذا فشل استرجاع بيانات التتبع، استمر مع بيانات الطلبية فقط
      }

      // تحديد المرحلة الحالية بناءً على حالة الطلبية
      int currentStage = 0;
      final status = orderData['status'] ?? 'pending';
      switch (status) {
        case 'pending':
          currentStage = 0; // استلام الطلبية
          break;
        case 'processing':
          currentStage = 1; // جاري المعالجة
          break;
        case 'shipping':
          currentStage = 2; // جاري الشحن
          break;
        case 'out_for_delivery':
          currentStage = 3; // جاري التوصيل
          break;
        case 'delivered':
          currentStage = 4; // تم التوزيع
          break;
        default:
          currentStage = 0;
      }

      setState(() {
        _orderData = orderData;
        _trackingData = trackingResponse;
        _addressData = orderData['addresses'];
        _currentStage = currentStage;
        _isLoading = false;
        _errorMessage = null; // تأكد من إزالة أي رسائل خطأ سابقة
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = e.toString();
      });
      debugPrint('خطأ في تحميل بيانات الطلبية: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(tr('track_order')), centerTitle: true),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Iconsax.warning_2, size: 64.sp, color: Colors.red),
                  SizedBox(height: 16.h),
                  Text(
                    _errorMessage!,
                    textAlign: TextAlign.center,
                    style: TextStyle(fontSize: 16.sp, color: Colors.red),
                  ),
                  SizedBox(height: 24.h),
                  ElevatedButton(
                    onPressed: _loadOrderData,
                    child: Text(tr('retry')),
                  ),
                ],
              ),
            )
          : RefreshIndicator(
              onRefresh: _loadOrderData,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Column(
                  children: [
                    // معلومات الطلبية
                    _buildOrderInfo(),

                    // شريط تتبع المراحل
                    _buildTrackingTimeline(),

                    // تفاصيل إضافية
                    _buildAdditionalDetails(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildOrderInfo() {
    if (_orderData == null) return const SizedBox.shrink();

    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رقم الطلبية
          Row(
            children: [
              Icon(
                Iconsax.document_code,
                size: 20.sp,
                color: Theme.of(context).colorScheme.primary,
              ),
              SizedBox(width: 8.w),
              Text(
                '${tr('order_number')}: #${_orderData!['id']}',
                style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.bold),
              ),
            ],
          ),

          Divider(height: 24.h),

          // عنوان التوصيل
          if (_addressData != null) ...[
            Text(
              tr('delivery_address'),
              style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8.h),
            Text(
              '${_addressData!['address'] ?? ''}, ${_addressData!['city'] ?? ''}, ${_addressData!['country'] ?? ''}',
              style: TextStyle(fontSize: 14.sp),
            ),
            Divider(height: 24.h),
          ],

          // تاريخ الطلبية
          if (_orderData!['created_at'] != null) ...[
            Row(
              children: [
                Icon(
                  Iconsax.calendar,
                  size: 20.sp,
                  color: Theme.of(context).colorScheme.primary,
                ),
                SizedBox(width: 8.w),
                Text(
                  '${tr('order_date')}: ${DateFormat('yyyy-MM-dd').format(DateTime.parse(_orderData!['created_at']))}',
                  style: TextStyle(fontSize: 14.sp),
                ),
              ],
            ),
            SizedBox(height: 12.h),
          ],

          // إجمالي الطلبية
          Row(
            children: [
              Icon(
                Iconsax.money,
                size: 20.sp,
                color: Theme.of(context).colorScheme.primary,
              ),
              SizedBox(width: 8.w),
              Text(
                '${tr('total')}: ${_orderData!['total_price']} ${tr('sar')}',
                style: TextStyle(fontSize: 14.sp),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTrackingTimeline() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            tr('order_tracking'),
            style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 24.h),

          // مراحل التتبع
          for (int i = 0; i < _stages.length; i++) _buildTrackingStep(i),
        ],
      ),
    );
  }

  Widget _buildTrackingStep(int index) {
    final bool isActive = index <= _currentStage;
    final bool isCurrent = index == _currentStage;

    return Row(
          children: [
            // الأيقونة
            Container(
              width: 40.w,
              height: 40.w,
              decoration: BoxDecoration(
                color: isActive
                    ? Theme.of(context).colorScheme.primary
                    : Colors.grey.shade300,
                shape: BoxShape.circle,
              ),
              child: Icon(
                _icons[index],
                color: isActive ? Colors.white : Colors.grey,
                size: 20.sp,
              ),
            ),

            SizedBox(width: 12.w),

            // النص والتاريخ
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _stages[index],
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: isCurrent
                          ? FontWeight.bold
                          : FontWeight.normal,
                      color: isActive
                          ? Theme.of(context).colorScheme.primary
                          : Colors.grey,
                    ),
                  ),

                  if (_trackingData != null && isActive) ...[
                    SizedBox(height: 4.h),
                    _buildTrackingDate(index),
                  ],
                ],
              ),
            ),
          ],
        )
        .animate(target: isActive ? 1 : 0)
        .fadeIn()
        .slideX(
          begin: 0.1,
          end: 0,
          curve: Curves.easeOutQuad,
          duration: const Duration(milliseconds: 300),
        )
        .then(delay: 100.ms)
        .then(delay: (index * 100).ms);
  }

  Widget _buildTrackingDate(int index) {
    String? dateText;

    if (_trackingData != null) {
      switch (index) {
        case 0: // استلام الطلبية
          dateText = _trackingData!['received_at'] != null
              ? DateFormat(
                  'yyyy-MM-dd HH:mm',
                ).format(DateTime.parse(_trackingData!['received_at']))
              : null;
          break;
        case 1: // جاري المعالجة
          dateText = _trackingData!['processing_at'] != null
              ? DateFormat(
                  'yyyy-MM-dd HH:mm',
                ).format(DateTime.parse(_trackingData!['processing_at']))
              : null;
          break;
        case 2: // جاري الشحن
          dateText = _trackingData!['shipping_at'] != null
              ? DateFormat(
                  'yyyy-MM-dd HH:mm',
                ).format(DateTime.parse(_trackingData!['shipping_at']))
              : null;
          break;
        case 3: // جاري التوصيل
          dateText = _trackingData!['out_for_delivery_at'] != null
              ? DateFormat(
                  'yyyy-MM-dd HH:mm',
                ).format(DateTime.parse(_trackingData!['out_for_delivery_at']))
              : null;
          break;
        case 4: // تم التوزيع
          dateText = _trackingData!['delivered_at'] != null
              ? DateFormat(
                  'yyyy-MM-dd HH:mm',
                ).format(DateTime.parse(_trackingData!['delivered_at']))
              : null;
          break;
      }
    }

    return dateText != null
        ? Text(
            dateText,
            style: TextStyle(
              fontSize: 12.sp,
              color: Theme.of(context).colorScheme.secondary,
            ),
          )
        : const SizedBox.shrink();
  }

  Widget _buildAdditionalDetails() {
    if (_orderData == null) return const SizedBox.shrink();

    // تحديد لون حالة الطلبية
    Color statusColor;
    String statusText;

    switch (_currentStage) {
      case 0:
        statusColor = Colors.orange;
        statusText = tr('pending');
        break;
      case 1:
        statusColor = Colors.blue;
        statusText = tr('processing');
        break;
      case 2:
        statusColor = Colors.indigo;
        statusText = tr('shipping');
        break;
      case 3:
        statusColor = Colors.deepPurple;
        statusText = tr('order_out_for_delivery');
        break;
      case 4:
        statusColor = Colors.green;
        statusText = tr('delivered');
        break;
      default:
        statusColor = Colors.grey;
        statusText = tr('pending');
    }

    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // حالة الطلبية
          Text(
            tr('order_status'),
            style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 12.h),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20.r),
              border: Border.all(color: statusColor),
            ),
            child: Text(
              statusText,
              style: TextStyle(
                color: statusColor,
                fontWeight: FontWeight.bold,
                fontSize: 14.sp,
              ),
            ),
          ),

          if (_orderData!['notes'] != null &&
              _orderData!['notes'].toString().isNotEmpty) ...[
            SizedBox(height: 16.h),
            Text(
              tr('notes'),
              style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8.h),
            Text(_orderData!['notes'], style: TextStyle(fontSize: 14.sp)),
          ],
        ],
      ),
    );
  }
}

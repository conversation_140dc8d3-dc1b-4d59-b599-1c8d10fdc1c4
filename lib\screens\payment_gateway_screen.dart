import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:iconsax/iconsax.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:soqia_joud/screens/order_tracking_screen.dart';

class PaymentGatewayScreen extends StatefulWidget {
  final double amount;
  final String orderId;
  
  const PaymentGatewayScreen({super.key, required this.amount, required this.orderId});

  @override
  State<PaymentGatewayScreen> createState() => _PaymentGatewayScreenState();
}

class _PaymentGatewayScreenState extends State<PaymentGatewayScreen> {
  final _formKey = GlobalKey<FormState>();
  
  // بيانات الدفع
  final TextEditingController _cardNumberController = TextEditingController();
  final TextEditingController _cardHolderController = TextEditingController();
  final TextEditingController _expiryDateController = TextEditingController();
  final TextEditingController _cvvController = TextEditingController();
  
  // طرق الدفع المتاحة
  final List<Map<String, dynamic>> _paymentMethods = [
    {
      'name': 'بطاقة الائتمان',
      'icon': Iconsax.card,
      'isSelected': true,
    },
    {
      'name': 'Apple Pay',
      'icon': Icons.apple,
      'isSelected': false,
    },
    {
      'name': 'Google Pay',
      'icon': Icons.android,
      'isSelected': false,
    },
  ];
  
  bool _isProcessing = false;
  
  @override
  void dispose() {
    _cardNumberController.dispose();
    _cardHolderController.dispose();
    _expiryDateController.dispose();
    _cvvController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('بوابة الدفع'),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // المبلغ المطلوب دفعه
            _buildAmountSection(),
            
            SizedBox(height: 24.h),
            
            // طرق الدفع
            Text(
              'اختر طريقة الدفع',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            _buildPaymentMethodSelector(),
            
            SizedBox(height: 24.h),
            
            // نموذج بيانات البطاقة
            _buildPaymentForm(),
            
            SizedBox(height: 24.h),
            
            // زر الدفع
            _buildPayButton(),
          ],
        ),
      ),
    );
  }
  
  Widget _buildAmountSection() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'المبلغ الإجمالي:',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            '${widget.amount} AED',
            style: TextStyle(
              fontSize: 20.sp,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ],
      ),
    ).animate()
      .fadeIn(duration: const Duration(milliseconds: 400))
      .slideY(begin: 0.2, end: 0, duration: const Duration(milliseconds: 400));
  }
  
  Widget _buildPaymentMethodSelector() {
    return SizedBox(
      height: 80.h,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _paymentMethods.length,
        itemBuilder: (context, index) {
          final method = _paymentMethods[index];
          return GestureDetector(
            onTap: () {
              setState(() {
                for (var i = 0; i < _paymentMethods.length; i++) {
                  _paymentMethods[i]['isSelected'] = i == index;
                }
              });
            },
            child: Container(
              width: 100.w,
              margin: EdgeInsets.only(right: 12.w),
              decoration: BoxDecoration(
                color: method['isSelected']
                    ? Theme.of(context).colorScheme.primary
                    : Colors.white,
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(
                  color: method['isSelected']
                      ? Theme.of(context).colorScheme.primary
                      : Colors.grey.shade300,
                  width: 1.5,
                ),
                boxShadow: method['isSelected']
                    ? [
                        BoxShadow(
                          color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ]
                    : null,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    method['icon'],
                    color: method['isSelected'] ? Colors.white : Colors.grey.shade700,
                    size: 24.sp,
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    method['name'],
                    style: TextStyle(
                      color: method['isSelected'] ? Colors.white : Colors.grey.shade700,
                      fontSize: 12.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ).animate()
            .fadeIn(duration: const Duration(milliseconds: 400), delay: Duration(milliseconds: index * 100))
            .slideX(begin: 0.2, end: 0, duration: const Duration(milliseconds: 400));
        },
      ),
    );
  }
  
  Widget _buildPaymentForm() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'تفاصيل البطاقة',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16.h),
          
          // رقم البطاقة
          TextFormField(
            controller: _cardNumberController,
            decoration: InputDecoration(
              labelText: 'رقم البطاقة',
              prefixIcon: const Icon(Iconsax.card),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            keyboardType: TextInputType.number,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'يرجى إدخال رقم البطاقة';
              }
              return null;
            },
          ),
          SizedBox(height: 16.h),
          
          // اسم حامل البطاقة
          TextFormField(
            controller: _cardHolderController,
            decoration: InputDecoration(
              labelText: 'اسم حامل البطاقة',
              prefixIcon: const Icon(Iconsax.user),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'يرجى إدخال اسم حامل البطاقة';
              }
              return null;
            },
          ),
          SizedBox(height: 16.h),
          
          // تاريخ الانتهاء و CVV
          Row(
            children: [
              // تاريخ الانتهاء
              Expanded(
                child: TextFormField(
                  controller: _expiryDateController,
                  decoration: InputDecoration(
                    labelText: 'تاريخ الانتهاء (MM/YY)',
                    prefixIcon: const Icon(Iconsax.calendar),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال تاريخ الانتهاء';
                    }
                    return null;
                  },
                ),
              ),
              SizedBox(width: 16.w),
              
              // CVV
              Expanded(
                child: TextFormField(
                  controller: _cvvController,
                  decoration: InputDecoration(
                    labelText: 'CVV',
                    prefixIcon: const Icon(Iconsax.security),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                  ),
                  keyboardType: TextInputType.number,
                  obscureText: true,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال رمز CVV';
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    ).animate()
      .fadeIn(duration: const Duration(milliseconds: 400), delay: const Duration(milliseconds: 200))
      .slideY(begin: 0.2, end: 0, duration: const Duration(milliseconds: 400));
  }
  
  Widget _buildPayButton() {
    return SizedBox(
      width: double.infinity,
      height: 50.h,
      child: ElevatedButton(
        onPressed: _isProcessing ? null : _processPayment,
        style: ElevatedButton.styleFrom(
          backgroundColor: Theme.of(context).colorScheme.primary,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.r),
          ),
          elevation: 2,
        ),
        child: _isProcessing
            ? const CircularProgressIndicator(color: Colors.white)
            : Text(
                'إتمام الدفع',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
      ),
    ).animate()
      .fadeIn(duration: const Duration(milliseconds: 400), delay: const Duration(milliseconds: 300))
      .slideY(begin: 0.2, end: 0, duration: const Duration(milliseconds: 400));
  }
  
  void _processPayment() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isProcessing = true;
      });
      
      // محاكاة عملية الدفع
      await Future.delayed(const Duration(seconds: 2));
      
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
        
        // عرض رسالة نجاح الدفع
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('تم الدفع بنجاح'),
            content: const Text('تم إتمام عملية الدفع بنجاح. يمكنك الآن تتبع طلبيتك.'),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context); // إغلاق الحوار
                  
                  // الانتقال إلى صفحة تتبع الطلبية
                  Navigator.pushReplacement(
                    context,
                    MaterialPageRoute(
                      builder: (context) => OrderTrackingScreen(
                        orderId: widget.orderId,
                      ),
                    ),
                  );
                },
                child: const Text('تتبع الطلبية'),
              ),
            ],
          ),
        );
      }
    }
  }
}

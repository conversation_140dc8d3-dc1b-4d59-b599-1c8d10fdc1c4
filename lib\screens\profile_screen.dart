import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:iconsax/iconsax.dart';
import 'package:soqia_joud/models/user.dart';
import 'package:soqia_joud/services/supabase_service.dart';
import 'package:soqia_joud/screens/login_screen.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  User? _user;
  bool _isLoading = true;
  bool _isEditing = false;
  
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emirateController = TextEditingController();
  final _addressController = TextEditingController();
  
  final List<String> _emirates = [
    'أبو ظبي',
    'دبي',
    'الشارقة',
    'عجمان',
    'أم القيوين',
    'رأس الخيمة',
    'الفجيرة'
  ];
  
  String? _selectedEmirate;

  @override
  void initState() {
    super.initState();
    _loadUserProfile();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _emirateController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  Future<void> _loadUserProfile() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final currentUser = SupabaseService.getCurrentUser();
      if (currentUser != null) {
        final userData = await SupabaseService.getUserProfile();
        
        if (userData != null) {
          setState(() {
            _user = User.fromJson(userData);
            _nameController.text = _user?.fullName ?? '';
            _phoneController.text = _user?.phone ?? '';
            _selectedEmirate = _user?.emirate;
            _addressController.text = _user?.address ?? '';
          });
        }
      } else {
        // Not logged in, redirect to login screen
        if (mounted) {
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(builder: (context) => const LoginScreen()),
          );
        }
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: ${e.toString()}')),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _updateProfile() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await SupabaseService.updateUserProfile(
        fullName: _nameController.text.trim(),
        phone: _phoneController.text.trim(),
        emirate: _selectedEmirate ?? '',
        address: _addressController.text.trim(),
      );

      // Reload user profile
      await _loadUserProfile();
      
      setState(() {
        _isEditing = false;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(tr('profile_updated'))),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: ${e.toString()}')),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _signOut() async {
    try {
      await SupabaseService.signOut();
      if (mounted) {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => const LoginScreen()),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: ${e.toString()}')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(tr('profile')),
        centerTitle: true,
        actions: [
          if (!_isEditing && !_isLoading)
            IconButton(
              icon: const Icon(Iconsax.edit),
              onPressed: () {
                setState(() {
                  _isEditing = true;
                });
              },
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: EdgeInsets.all(24.w),
              child: _isEditing ? _buildEditForm() : _buildProfileView(),
            ),
    );
  }

  Widget _buildProfileView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(height: 20.h),
        
        // Profile Image
        CircleAvatar(
          radius: 60.r,
          backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.2),
          child: Icon(
            Iconsax.user,
            size: 60.r,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
        
        SizedBox(height: 24.h),
        
        // User Name
        Text(
          _user?.fullName ?? tr('guest'),
          style: TextStyle(
            fontSize: 24.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        
        SizedBox(height: 8.h),
        
        // User Email
        Text(
          SupabaseService.getCurrentUser()?.email ?? '',
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.grey,
          ),
        ),
        
        SizedBox(height: 32.h),
        
        // Info Cards
        _buildInfoCard(Iconsax.call, tr('phone'), _user?.phone ?? ''),
        _buildInfoCard(Iconsax.location, tr('emirate'), _user?.emirate ?? ''),
        _buildInfoCard(Iconsax.location_tick, tr('address'), _user?.address ?? ''),
        
        SizedBox(height: 40.h),
        
        // Logout Button
        SizedBox(
          width: double.infinity,
          height: 56.h,
          child: ElevatedButton.icon(
            onPressed: _signOut,
            icon: const Icon(Iconsax.logout),
            label: Text(tr('logout')),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInfoCard(IconData icon, String title, String value) {
    return Card(
      margin: EdgeInsets.only(bottom: 16.h),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Row(
          children: [
            Icon(icon, color: Theme.of(context).colorScheme.primary),
            SizedBox(width: 16.w),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.grey,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEditForm() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          SizedBox(height: 20.h),
          
          // Profile Image
          Center(
            child: Stack(
              children: [
                CircleAvatar(
                  radius: 60.r,
                  backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.2),
                  child: const Icon(Iconsax.user, size: 40)
                ),
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: CircleAvatar(
                    radius: 20.r,
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    child: IconButton(
                      icon: const Icon(Iconsax.camera, color: Colors.white),
                      onPressed: () {
                        // TODO: Implement image upload
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          SizedBox(height: 32.h),
          
          // Name Field
          TextFormField(
            controller: _nameController,
            decoration: InputDecoration(
              labelText: tr('name'),
              prefixIcon: const Icon(Iconsax.user),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return tr('name_required');
              }
              return null;
            },
          ),
          
          SizedBox(height: 16.h),
          
          // Phone Field
          TextFormField(
            controller: _phoneController,
            decoration: InputDecoration(
              labelText: tr('phone'),
              prefixIcon: const Icon(Iconsax.call),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            keyboardType: TextInputType.phone,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return tr('phone_required');
              }
              return null;
            },
          ),
          
          SizedBox(height: 16.h),
          
          // Emirate Dropdown
          DropdownButtonFormField<String>(
            value: _selectedEmirate,
            decoration: InputDecoration(
              labelText: tr('emirate'),
              prefixIcon: const Icon(Iconsax.location),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            items: _emirates.map((emirate) {
              return DropdownMenuItem<String>(
                value: emirate,
                child: Text(emirate),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedEmirate = value;
              });
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return tr('emirate_required');
              }
              return null;
            },
          ),
          
          SizedBox(height: 16.h),
          
          // Address Field
          TextFormField(
            controller: _addressController,
            decoration: InputDecoration(
              labelText: tr('address'),
              prefixIcon: const Icon(Iconsax.location_tick),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            maxLines: 2,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return tr('address_required');
              }
              return null;
            },
          ),
          
          SizedBox(height: 32.h),
          
          // Action Buttons
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _isEditing = false;
                      // Reset form
                      _nameController.text = _user?.fullName ?? '';
                      _phoneController.text = _user?.phone ?? '';
                      _selectedEmirate = _user?.emirate;
                      _addressController.text = _user?.address ?? '';
                    });
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey.shade200,
                    foregroundColor: Colors.black,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(tr('cancel')),
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: ElevatedButton(
                  onPressed: _updateProfile,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(tr('save')),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

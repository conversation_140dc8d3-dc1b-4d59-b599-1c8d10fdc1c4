import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:iconsax/iconsax.dart';
import 'package:soqia_joud/services/supabase_service.dart';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emirateController = TextEditingController();
  final _addressController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  
  bool _isLoading = false;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  String? _errorMessage;
  
  final List<String> _emirates = [
    'أبو ظبي',
    'دبي',
    'الشارقة',
    'عجمان',
    'أم القيوين',
    'رأس الخيمة',
    'الفجيرة'
  ];
  
  String? _selectedEmirate;

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _emirateController.dispose();
    _addressController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Future<void> _register() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final response = await SupabaseService.signUp(
        email: _emailController.text.trim(),
        password: _passwordController.text,
        fullName: _nameController.text.trim(),
        phone: _phoneController.text.trim(),
        emirate: _selectedEmirate ?? '',
        address: _addressController.text.trim(),
      );

      if (response.user != null) {
        // No need to update profile here as it's handled by the trigger in Supabase
        if (mounted) {
          // إظهار مربع حوار لتأكيد البريد الإلكتروني
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) => AlertDialog(
              title: Text(tr('email_verification')),
              content: Text(tr('email_verification_message')),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context); // إغلاق مربع الحوار
                    Navigator.pop(context); // العودة إلى شاشة تسجيل الدخول
                  },
                  child: Text(tr('ok')),
                ),
              ],
            ),
          );
        }
      } else {
        setState(() {
          _errorMessage = tr('register_failed');
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(tr('register')),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(24.w),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              SizedBox(height: 20.h),
              
              // Name Field
              _buildTextField(
                controller: _nameController,
                label: tr('name'),
                icon: Iconsax.user,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return tr('name_required');
                  }
                  return null;
                },
              ),
              
              SizedBox(height: 16.h),
              
              // Email Field
              _buildTextField(
                controller: _emailController,
                label: tr('email'),
                icon: Iconsax.sms,
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return tr('email_required');
                  }
                  if (!value.contains('@') || !value.contains('.')) {
                    return tr('invalid_email');
                  }
                  return null;
                },
              ),
              
              SizedBox(height: 16.h),
              
              // Phone Field
              _buildTextField(
                controller: _phoneController,
                label: tr('phone'),
                icon: Iconsax.call,
                keyboardType: TextInputType.phone,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return tr('phone_required');
                  }
                  return null;
                },
              ),
              
              SizedBox(height: 16.h),
              
              // Emirate Dropdown
              DropdownButtonFormField<String>(
                value: _selectedEmirate,
                decoration: InputDecoration(
                  labelText: tr('emirate'),
                  prefixIcon: Icon(Iconsax.location),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                items: _emirates.map((emirate) {
                  return DropdownMenuItem<String>(
                    value: emirate,
                    child: Text(emirate),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedEmirate = value;
                  });
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return tr('emirate_required');
                  }
                  return null;
                },
              ),
              
              SizedBox(height: 16.h),
              
              // Address Field
              _buildTextField(
                controller: _addressController,
                label: tr('address'),
                icon: Iconsax.location_tick,
                maxLines: 2,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return tr('address_required');
                  }
                  return null;
                },
              ),
              
              SizedBox(height: 16.h),
              
              // Password Field
              _buildPasswordField(
                controller: _passwordController,
                label: tr('password'),
                obscureText: _obscurePassword,
                toggleObscure: () {
                  setState(() {
                    _obscurePassword = !_obscurePassword;
                  });
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return tr('password_required');
                  }
                  if (value.length < 6) {
                    return tr('password_too_short');
                  }
                  return null;
                },
              ),
              
              SizedBox(height: 16.h),
              
              // Confirm Password Field
              _buildPasswordField(
                controller: _confirmPasswordController,
                label: tr('confirm_password'),
                obscureText: _obscureConfirmPassword,
                toggleObscure: () {
                  setState(() {
                    _obscureConfirmPassword = !_obscureConfirmPassword;
                  });
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return tr('confirm_password_required');
                  }
                  if (value != _passwordController.text) {
                    return tr('passwords_dont_match');
                  }
                  return null;
                },
              ),
              
              SizedBox(height: 24.h),
              
              // Error Message
              if (_errorMessage != null)
                Container(
                  padding: EdgeInsets.symmetric(vertical: 8.h),
                  child: Text(
                    _errorMessage!,
                    style: TextStyle(
                      color: Colors.red,
                      fontSize: 14.sp,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              
              // Register Button
              SizedBox(
                height: 56.h,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _register,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: _isLoading
                      ? CircularProgressIndicator(color: Colors.white)
                      : Text(
                          tr('register'),
                          style: TextStyle(
                            fontSize: 18.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ),
              
              SizedBox(height: 24.h),
              
              // Login Link
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(tr('already_have_account')),
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    child: Text(tr('login')),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType keyboardType = TextInputType.text,
    int maxLines = 1,
    required String? Function(String?) validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      maxLines: maxLines,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      validator: validator,
    );
  }
  
  Widget _buildPasswordField({
    required TextEditingController controller,
    required String label,
    required bool obscureText,
    required VoidCallback toggleObscure,
    required String? Function(String?) validator,
  }) {
    return TextFormField(
      controller: controller,
      obscureText: obscureText,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(Iconsax.lock),
        suffixIcon: IconButton(
          icon: Icon(
            obscureText ? Iconsax.eye_slash : Iconsax.eye,
          ),
          onPressed: toggleObscure,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      validator: validator,
    );
  }
}

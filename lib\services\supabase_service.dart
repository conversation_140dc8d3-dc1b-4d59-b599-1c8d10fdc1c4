import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class SupabaseService {
  static SupabaseClient? _client;

  static Future<void> initialize() async {
    final supabaseUrl = 'https://fxkqsdnoouygdynsozuc.supabase.co';
    final supabaseAnonKey =
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4a3FzZG5vb3V5Z2R5bnNvenVjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwNjcyMzIsImV4cCI6MjA2NDY0MzIzMn0.1tmxKR6thhjUt7lOS62HOTns4X-O38abP9QeaWbQ96c';

    await Supabase.initialize(
      url: supabaseUrl,
      anonKey: supabaseAnonKey,
      debug: kDebugMode,
    );

    _client = Supabase.instance.client;
  }

  static SupabaseClient get client {
    if (_client == null) {
      throw Exception(
        'Supabase client not initialized. Call initialize() first.',
      );
    }
    return _client!;
  }

  // Authentication methods
  static Future<AuthResponse> signUp({
    required String email,
    required String password,
    required String fullName,
    required String phone,
    required String emirate,
    required String address,
  }) async {
    final response = await client.auth.signUp(
      email: email,
      password: password,
      data: {
        'full_name': fullName,
        'phone': phone,
        'emirate': emirate,
        'address': address,
      },
    );
    return response;
  }

  // Get current user
  static User? getCurrentUser() {
    return client.auth.currentUser;
  }

  // Get current user ID
  static String? getCurrentUserId() {
    return client.auth.currentUser?.id;
  }

  static Future<AuthResponse> signIn({
    required String email,
    required String password,
  }) async {
    final response = await client.auth.signInWithPassword(
      email: email,
      password: password,
    );
    return response;
  }

  static Future<void> signOut() async {
    await client.auth.signOut();
  }

  static User? get currentUser => client.auth.currentUser;

  static Stream<AuthState> get onAuthStateChange =>
      client.auth.onAuthStateChange;

  static Future<bool> isAuthenticated() async {
    return currentUser != null;
  }

  // Database methods
  static Future<List<Map<String, dynamic>>> getOrders() async {
    final response = await client
        .from('orders')
        .select()
        .eq('user_id', currentUser?.id ?? '')
        .order('created_at', ascending: false);

    return response;
  }

  // This method has been replaced by the new createOrder method below

  static Future<List<Map<String, dynamic>>> getAddresses() async {
    final response = await client
        .from('addresses')
        .select()
        .eq('user_id', currentUser?.id ?? '');

    return response;
  }

  // دالة createAddress القديمة تم استبدالها بدالة addAddress الجديدة
  @deprecated
  static Future<void> createAddress({
    required String name,
    required String emirate,
    required String detailedAddress,
    bool isDefault = false,
  }) async {
    // استخدم الدالة الجديدة بدلاً من هذه
    return addAddress(
      addressName: name,
      emirate: emirate,
      area: detailedAddress.split(',').first,
      street: detailedAddress.contains(',')
          ? detailedAddress.split(',').last
          : '',
      isDefault: isDefault,
    );
  }

  // User profile methods
  static Future<void> updateUserProfile({
    required String fullName,
    required String phone,
    required String emirate,
    required String address,
  }) async {
    final currentUser = client.auth.currentUser;
    if (currentUser == null) throw Exception('User not authenticated');

    await client.from('profiles').upsert({
      'id': currentUser.id,
      'full_name': fullName,
      'email': currentUser.email,
      'phone': phone,
      'emirate': emirate,
      'address': address,
      'updated_at': DateTime.now().toIso8601String(),
    });
  }

  // Get user profile
  static Future<Map<String, dynamic>?> getUserProfile() async {
    final currentUser = client.auth.currentUser;
    if (currentUser == null) return null;

    final response = await client
        .from('profiles')
        .select()
        .eq('id', currentUser.id)
        .single();

    return response;
  }

  // Address methods
  static Future<List<dynamic>> getUserAddresses() async {
    final currentUser = client.auth.currentUser;
    if (currentUser == null) throw Exception('User not authenticated');

    final response = await client
        .from('addresses')
        .select()
        .eq('user_id', currentUser.id)
        .order('is_default', ascending: false);

    return response;
  }

  static Future<void> addAddress({
    required String addressName,
    required String emirate,
    required String area,
    required String street,
    String? buildingNumber,
    String? floorNumber,
    String? apartmentNumber,
    String? landmark,
    String? phone,
    required bool isDefault,
  }) async {
    final currentUser = client.auth.currentUser;
    if (currentUser == null) throw Exception('User not authenticated');

    // If setting as default, unset any existing default address
    if (isDefault) {
      await client
          .from('addresses')
          .update({'is_default': false})
          .eq('user_id', currentUser.id)
          .eq('is_default', true);
    }

    await client.from('addresses').insert({
      'user_id': currentUser.id,
      'address_name': addressName,
      'emirate': emirate,
      'area': area,
      'street': street,
      'building_number': buildingNumber,
      'floor_number': floorNumber,
      'apartment_number': apartmentNumber,
      'landmark': landmark,
      'phone': phone,
      'is_default': isDefault,
    });
  }

  static Future<void> updateAddress({
    required String id,
    required String addressName,
    required String emirate,
    required String area,
    required String street,
    String? buildingNumber,
    String? floorNumber,
    String? apartmentNumber,
    String? landmark,
    String? phone,
    required bool isDefault,
  }) async {
    final currentUser = client.auth.currentUser;
    if (currentUser == null) throw Exception('User not authenticated');

    // If setting as default, unset any existing default address
    if (isDefault) {
      await client
          .from('addresses')
          .update({'is_default': false})
          .eq('user_id', currentUser.id)
          .eq('is_default', true);
    }

    await client
        .from('addresses')
        .update({
          'address_name': addressName,
          'emirate': emirate,
          'area': area,
          'street': street,
          'building_number': buildingNumber,
          'floor_number': floorNumber,
          'apartment_number': apartmentNumber,
          'landmark': landmark,
          'phone': phone,
          'is_default': isDefault,
          'updated_at': DateTime.now().toIso8601String(),
        })
        .eq('id', id)
        .eq('user_id', currentUser.id);
  }

  static Future<void> deleteAddress(String id) async {
    final currentUser = client.auth.currentUser;
    if (currentUser == null) throw Exception('User not authenticated');

    await client
        .from('addresses')
        .delete()
        .eq('id', id)
        .eq('user_id', currentUser.id);
  }

  static Future<void> setDefaultAddress(String id) async {
    final currentUser = client.auth.currentUser;
    if (currentUser == null) throw Exception('User not authenticated');

    // Unset any existing default address
    await client
        .from('addresses')
        .update({'is_default': false})
        .eq('user_id', currentUser.id)
        .eq('is_default', true);

    // Set the new default address
    await client
        .from('addresses')
        .update({'is_default': true})
        .eq('id', id)
        .eq('user_id', currentUser.id);
  }

  // Order methods
  static Future<String> createOrder({
    required int quantity,
    required double unitPrice,
    required double totalPrice,
    required String addressId,
    required String status,
  }) async {
    final currentUser = client.auth.currentUser;
    if (currentUser == null) throw Exception('User not authenticated');

    final now = DateTime.now();

    try {
      print('🔄 بدء إنشاء طلبية جديدة...');
      print(
        'البيانات: quantity=$quantity, unitPrice=$unitPrice, totalPrice=$totalPrice, addressId=$addressId',
      );

      // استخدام أسماء الأعمدة الصحيحة في قاعدة البيانات
      final response = await client
          .from('orders')
          .insert({
            'user_id': currentUser.id,
            'quantity': quantity,
            'unit_price': unitPrice.toInt(), // تحويل إلى عدد صحيح
            'total_price': totalPrice.toInt(), // تحويل إلى عدد صحيح
            'address_id': addressId,
            'status': status,
            'order_date': now.toIso8601String(),
            // إزالة order_number مؤقتاً لتجنب التضارب
          })
          .select('id')
          .single();

      print('✅ تم إنشاء الطلبية بنجاح: ${response.toString()}');

      // التعامل مع معرف الطلبية سواء كان رقم أو نص
      final orderId = response['id'].toString();

      // إنشاء سجل تتبع بسيط (بدون الاعتماد على trigger)
      try {
        print('🔄 إنشاء سجل تتبع للطلبية...');

        // التحقق أولاً من وجود سجل تتبع
        final existingTracking = await client
            .from('order_tracking')
            .select('id')
            .eq('order_id', response['id'])
            .maybeSingle();

        if (existingTracking == null) {
          // إنشاء سجل تتبع جديد فقط إذا لم يكن موجوداً
          await client.from('order_tracking').insert({
            'order_id': response['id'],
            'status': 'pending',
            'notes': 'تم استلام الطلبية بنجاح',
            'received_at': now.toIso8601String(),
          });
          print('✅ تم إنشاء سجل تتبع للطلبية بنجاح');
        } else {
          print('✅ سجل التتبع موجود بالفعل');
        }
      } catch (trackingError) {
        print('❌ خطأ في إنشاء سجل تتبع الطلبية: $trackingError');
        // لا نرمي خطأ هنا لأن الطلبية تم إنشاؤها بنجاح
      }

      return orderId;
    } catch (e) {
      print('❌ خطأ في إنشاء الطلبية: $e');
      throw Exception('فشل في إنشاء الطلبية: ${e.toString()}');
    }
  }

  static Future<List<dynamic>> getUserOrders() async {
    final currentUser = client.auth.currentUser;
    if (currentUser == null) throw Exception('User not authenticated');

    final response = await client
        .from('orders')
        .select('''
          *,
          addresses!inner(*)
        ''')
        .eq('user_id', currentUser.id)
        .order('order_date', ascending: false);

    return response;
  }

  static Future<Map<String, dynamic>> getOrderDetails(String orderId) async {
    final currentUser = client.auth.currentUser;
    if (currentUser == null) throw Exception('User not authenticated');

    try {
      // محاولة استرجاع الطلبية باستخدام المعرف كما هو
      final response = await client
          .from('orders')
          .select('''
            *,
            addresses(*),
            order_tracking(*)
          ''')
          .eq('id', orderId)
          .eq('user_id', currentUser.id)
          .single();

      return response;
    } catch (e) {
      // طباعة الخطأ للتشخيص
      print('Error fetching order details: $e');

      // إعادة المحاولة باستخدام معرف رقمي إذا كان ذلك ممكناً
      try {
        int? numericId = int.tryParse(orderId);
        if (numericId != null) {
          final response = await client
              .from('orders')
              .select('''
                *,
                addresses(*),
                order_tracking(*)
              ''')
              .eq('id', numericId)
              .eq('user_id', currentUser.id)
              .single();

          return response;
        }
      } catch (e2) {
        print('Error in second attempt: $e2');
      }

      // إذا فشلت جميع المحاولات، أعد إلقاء الخطأ الأصلي
      throw e;
    }
  }

  // Admin methods
  static Future<List<Map<String, dynamic>>> getAllOrders() async {
    final response = await client
        .from('orders')
        .select('*, profiles(*)')
        .order('created_at', ascending: false);

    return response;
  }

  static Future<void> updateOrderStatus({
    required int orderId,
    required String status,
  }) async {
    await client.from('orders').update({'status': status}).eq('id', orderId);
  }

  static Future<Map<String, dynamic>> getStatistics() async {
    final allOrders = await client.from('orders').select('status, total_price');

    int totalOrders = allOrders.length;
    int pendingOrders = allOrders
        .where((order) => order['status'] == 'pending')
        .length;
    int deliveringOrders = allOrders
        .where((order) => order['status'] == 'delivering')
        .length;
    int deliveredOrders = allOrders
        .where((order) => order['status'] == 'delivered')
        .length;

    double totalRevenue = allOrders.fold(
      0.0,
      (sum, order) => sum + (order['total_price'] as double),
    );

    return {
      'total_orders': totalOrders,
      'pending_orders': pendingOrders,
      'delivering_orders': deliveringOrders,
      'delivered_orders': deliveredOrders,
      'total_revenue': totalRevenue,
    };
  }

  /// تحديث دور المستخدم عن طريق البريد الإلكتروني
  static Future<bool> updateUserRoleByEmail(
    String email,
    String newRole,
  ) async {
    try {
      // البحث عن المستخدم بواسطة البريد الإلكتروني في جدول profiles
      final userResponse = await client
          .from('profiles')
          .select('id')
          .eq('email', email)
          .maybeSingle();

      if (userResponse == null) {
        print('لم يتم العثور على المستخدم بالبريد الإلكتروني: $email');
        return false;
      }

      final userId = userResponse['id'];

      // تحديث دور المستخدم في جدول profiles
      await client.from('profiles').update({'role': newRole}).eq('id', userId);

      print('تم تحديث دور المستخدم $email إلى $newRole بنجاح');
      return true;
    } catch (e) {
      print('خطأ في تحديث دور المستخدم: $e');
      return false;
    }
  }
}

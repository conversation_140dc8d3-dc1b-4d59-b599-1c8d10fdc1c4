import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:iconsax/iconsax.dart';
import 'package:flutter_animate/flutter_animate.dart';

class AppDrawer extends StatelessWidget {
  final Function(ThemeMode) onThemeChanged;
  final Function(Locale) onLanguageChanged;
  final ThemeMode currentThemeMode;

  const AppDrawer({
    Key? key,
    required this.onThemeChanged,
    required this.onLanguageChanged,
    required this.currentThemeMode,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final primaryColor = Theme.of(context).colorScheme.primary;
    final isArabic = context.locale.languageCode == 'ar';

    return Drawer(
      child: SingleChildScrollView(
        padding: EdgeInsets.zero,
        child: Column(
          children: [
          // Drawer Header
          DrawerHeader(
            decoration: BoxDecoration(
              color: primaryColor,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CircleAvatar(
                  radius: 40.r,
                  backgroundColor: Colors.white,
                  child: Icon(
                    Iconsax.user,
                    size: 40.r,
                    color: primaryColor,
                  ),
                ),
                SizedBox(height: 10.h),
                Text(
                  tr('app_name'),
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ).animate()
            .fadeIn(duration: const Duration(milliseconds: 300))
            .slide(begin: const Offset(0, -0.2)),

          // Profile
          ListTile(
            leading: const Icon(Iconsax.profile_circle),
            title: Text(tr('profile')),
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(context, '/profile');
            },
          ).animate()
            .fadeIn(duration: const Duration(milliseconds: 300), delay: const Duration(milliseconds: 100))
            .slide(begin: const Offset(-0.2, 0)),
            
          // My Orders
          ListTile(
            leading: const Icon(Iconsax.bag),
            title: Text(tr('my_orders')),
            onTap: () {
              Navigator.pop(context);
              Navigator.pushNamed(context, '/my-orders');
            },
          ).animate()
            .fadeIn(duration: const Duration(milliseconds: 300), delay: const Duration(milliseconds: 150))
            .slide(begin: const Offset(-0.2, 0)),

          // Theme Toggle
          ExpansionTile(
            leading: const Icon(Iconsax.moon),
            title: Text(tr('theme')),
            children: [
              ListTile(
                leading: const Icon(Iconsax.sun_1),
                title: Text(tr('light_theme')),
                trailing: currentThemeMode == ThemeMode.light
                    ? Icon(Iconsax.tick_circle, color: primaryColor)
                    : null,
                onTap: () {
                  onThemeChanged(ThemeMode.light);
                  Navigator.pop(context);
                },
              ),
              ListTile(
                leading: const Icon(Iconsax.moon),
                title: Text(tr('dark_theme')),
                trailing: currentThemeMode == ThemeMode.dark
                    ? Icon(Iconsax.tick_circle, color: primaryColor)
                    : null,
                onTap: () {
                  onThemeChanged(ThemeMode.dark);
                  Navigator.pop(context);
                },
              ),
            ],
          ).animate()
            .fadeIn(duration: const Duration(milliseconds: 300), delay: const Duration(milliseconds: 200))
            .slide(begin: const Offset(-0.2, 0)),

          // Language Toggle
          ExpansionTile(
            leading: const Icon(Iconsax.language_square),
            title: Text(tr('language')),
            children: [
              ListTile(
                leading: const Text('🇦🇪'),
                title: Text(tr('arabic')),
                trailing: isArabic
                    ? Icon(Iconsax.tick_circle, color: primaryColor)
                    : null,
                onTap: () {
                  onLanguageChanged(const Locale('ar'));
                  Navigator.pop(context);
                },
              ),
              ListTile(
                leading: const Text('🇬🇧'),
                title: Text(tr('english')),
                trailing: !isArabic
                    ? Icon(Iconsax.tick_circle, color: primaryColor)
                    : null,
                onTap: () {
                  onLanguageChanged(const Locale('en'));
                  Navigator.pop(context);
                },
              ),
            ],
          ).animate()
            .fadeIn(duration: const Duration(milliseconds: 300), delay: const Duration(milliseconds: 300))
            .slide(begin: const Offset(-0.2, 0)),

          const Divider(),

          // Donate Meals
          ListTile(
            leading: const Icon(Iconsax.heart),
            title: Text(tr('donate_meals')),
            onTap: () {
              Navigator.pop(context);
              // TODO: Navigate to donate meals screen
            },
          ).animate()
            .fadeIn(duration: const Duration(milliseconds: 300), delay: const Duration(milliseconds: 400))
            .slide(begin: const Offset(-0.2, 0)),

          // About Sharjah Charity
          ListTile(
            leading: const Icon(Iconsax.info_circle),
            title: Text(tr('about_sharjah_charity')),
            onTap: () {
              Navigator.pop(context);
              // TODO: Navigate to about screen
            },
          ).animate()
            .fadeIn(duration: const Duration(milliseconds: 300), delay: const Duration(milliseconds: 500))
            .slide(begin: const Offset(-0.2, 0)),

          // Latest News
          ListTile(
            leading: const Icon(Iconsax.notification),
            title: Text(tr('latest_news')),
            onTap: () {
              Navigator.pop(context);
              // TODO: Navigate to news screen
            },
          ).animate()
            .fadeIn(duration: const Duration(milliseconds: 300), delay: const Duration(milliseconds: 600))
            .slide(begin: const Offset(-0.2, 0)),

          // Contact Us
          ListTile(
            leading: const Icon(Iconsax.call),
            title: Text(tr('contact_us')),
            onTap: () {
              Navigator.pop(context);
              // TODO: Navigate to contact screen
            },
          ).animate()
            .fadeIn(duration: const Duration(milliseconds: 300), delay: const Duration(milliseconds: 700))
            .slide(begin: const Offset(-0.2, 0)),

          // Chat with Support
          ListTile(
            leading: const Icon(Iconsax.message),
            title: Text(tr('chat_with_support')),
            onTap: () {
              Navigator.pop(context);
              // TODO: Navigate to chat screen
            },
          ).animate()
            .fadeIn(duration: const Duration(milliseconds: 300), delay: const Duration(milliseconds: 800))
            .slide(begin: const Offset(-0.2, 0)),
        ],
      ),
    ));
  }
}

-- حذف العمود total_amount من جدول orders
-- والاحتفاظ بـ total_price فقط

-- 1. التحقق من البيانات الموجودة
SELECT 
    COUNT(*) as total_orders,
    COUNT(total_amount) as orders_with_total_amount,
    COUNT(total_price) as orders_with_total_price
FROM orders;

-- 2. نسخ البيانات من total_amount إلى total_price إذا كانت total_price فارغة
UPDATE orders 
SET total_price = total_amount 
WHERE total_price IS NULL AND total_amount IS NOT NULL;

-- 3. حذف العمود total_amount
ALTER TABLE orders DROP COLUMN IF EXISTS total_amount;

-- 4. التحقق من النتيجة
SELECT 
    column_name, 
    data_type, 
    is_nullable 
FROM information_schema.columns 
WHERE table_name = 'orders' 
AND column_name IN ('total_amount', 'total_price')
ORDER BY column_name;

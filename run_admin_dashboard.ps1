# لوحة التحكم الإدارية - نظام حجز وتشغيل المركبات الحكومية
# Admin Dashboard - Government Vehicle Booking System

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    تشغيل لوحة التحكم الإدارية" -ForegroundColor Yellow
Write-Host "    Admin Dashboard Launcher" -ForegroundColor Yellow  
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "جاري فحص متطلبات النظام..." -ForegroundColor Green
Write-Host "Checking system requirements..." -ForegroundColor Green

# فحص Flutter
try {
    $flutterVersion = flutter --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Flutter متوفر" -ForegroundColor Green
        Write-Host "✓ Flutter available" -ForegroundColor Green
    }
} catch {
    Write-Host "✗ Flutter غير متوفر - يرجى تثبيت Flutter أولاً" -ForegroundColor Red
    Write-Host "✗ Flutter not found - Please install Flutter first" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج / Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "جاري تشغيل لوحة التحكم الإدارية..." -ForegroundColor Yellow
Write-Host "Starting Admin Dashboard..." -ForegroundColor Yellow
Write-Host ""

# تشغيل لوحة التحكم
try {
    flutter run -t lib/main_admin.dart -d windows --release
} catch {
    Write-Host ""
    Write-Host "حدث خطأ أثناء تشغيل لوحة التحكم" -ForegroundColor Red
    Write-Host "Error occurred while starting the dashboard" -ForegroundColor Red
}

Write-Host ""
Write-Host "تم إغلاق لوحة التحكم" -ForegroundColor Yellow
Write-Host "Admin Dashboard Closed" -ForegroundColor Yellow
Read-Host "اضغط Enter للخروج / Press Enter to exit"
